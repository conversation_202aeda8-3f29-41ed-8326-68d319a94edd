import asyncio
from playwright.async_api import async_playwright
import time

async def run():
    async with async_playwright() as p:
        # 设置浏览器启动选项
        browser = await p.chromium.launch(headless=False)
        # 创建页面时设置视口分辨率
        page = await browser.new_page(viewport={"width": 1920, "height": 1080})
        
        # 访问特定坐标的 Google Maps 位置
        # 使用更明确的URL格式确保标记显示
        await page.goto("https://www.google.com/maps/place/30.5796556,104.0647198/@30.5796556,104.0647198,15z")
        
        # 等待页面完全加载
        await page.wait_for_load_state("networkidle")
        
        # 等待地图加载完成
        try:
            await page.wait_for_selector('div[aria-label*="地图"], div[aria-label*="Map"]', state="visible", timeout=30000)
        except:
            print("无法找到地图元素，但仍继续执行")
        
        # 等待标记加载
        await page.wait_for_timeout(3000)
        
        # 更温和的侧边栏处理方法
        try:
            # 只使用ESC键关闭信息窗口，这通常不会影响标记
            await page.keyboard.press("Escape")
            await page.wait_for_timeout(1000)
            
            # 使用更精确的JavaScript方法，只隐藏侧边栏信息部分
            await page.evaluate("""
            () => {
                // 只隐藏信息面板，不影响标记
                const infoPanels = document.querySelectorAll('.section-layout.section-scrollbox');
                infoPanels.forEach(panel => {
                    if(panel && panel.offsetParent !== null) {
                        panel.style.display = 'none';
                        console.log('隐藏了信息面板');
                    }
                });
                
                // 隐藏搜索框和其他UI元素，但保留地图控件
                const uiElements = document.querySelectorAll('.searchbox');
                uiElements.forEach(el => {
                    if(el && el.offsetParent !== null) {
                        el.style.opacity = '0.1';
                        console.log('降低了搜索框透明度');
                    }
                });
                
                // 确保标记保持可见并增强其可见性
                const allMapElements = document.querySelectorAll('*');
                for (const el of allMapElements) {
                    if (el.tagName === 'IMG' && 
                        (el.src.includes('marker') || 
                         el.src.includes('pin') || 
                         el.src.includes('maps.gstatic.com/mapfiles'))) {
                        el.style.visibility = 'visible';
                        el.style.opacity = '1';
                        el.style.zIndex = '9999';
                        console.log('增强了标记可见性');
                    }
                }
            }
            """)
            
            print("已使用温和方法处理侧边栏，保留标记")
        except Exception as e:
            print(f"处理侧边栏时出错: {e}")
        
        # 设置缩放
        await page.evaluate("document.body.style.zoom = '100%'")
        
        # 等待UI更新
        await page.wait_for_timeout(1000)
        
        # 截图
        await page.screenshot(
            path="location_screenshot.png", 
            full_page=True
        )
        
        await browser.close()
        print("带标记的地图截图已保存为 location_screenshot.png")

asyncio.run(run())