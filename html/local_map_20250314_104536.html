
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>轨迹地图</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
        <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
        <style>
            #map {
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
            }
            .location-label {
                background: none;
                border: none;
                box-shadow: none;
                color: red;
                font-weight: bold;
                font-size: 16px;
                text-shadow: 1px 1px 2px white;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            // 位置数据
            const locations = [{"name": "\u5c45\u4f4f\u5730", "lat": 25.0077, "lng": 121.5372, "showRedBox": true, "details": "\u8fd9\u662f\u5c45\u4f4f\u5730\u7684\u8be6\u7ec6\u4fe1\u606f", "timestamp": 1624240800}, {"name": "\u4e2d\u6b63\u8425\u533a", "lat": 25.0419, "lng": 121.5258, "details": "\u8fd9\u662f\u4e2d\u6b63\u8425\u533a\u7684\u8be6\u7ec6\u4fe1\u606f", "timestamp": 1624248000}, {"name": "\u5b66\u6821", "lat": 25.0426, "lng": 121.5347, "details": "\u8fd9\u662f\u5b66\u6821\u7684\u8be6\u7ec6\u4fe1\u606f", "timestamp": 1624255200}];
            
            // 初始化地图
            const map = L.map('map');
            
            // 添加OpenStreetMap底图
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }).addTo(map);
            
            // 创建一个用于绘制轨迹的线条
            const polylinePoints = [];
            const bounds = L.latLngBounds();
            
            // 为每个位置添加标记
            locations.forEach((location, index) => {
                const latlng = L.latLng(location.lat, location.lng);
                polylinePoints.push(latlng);
                bounds.extend(latlng);
                
                // 创建蓝色圆形标记
                const marker = L.circleMarker(latlng, {
                    radius: 8,
                    fillColor: '#0066FF',
                    color: '#FFFFFF',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 1
                }).addTo(map);
                
                // 添加位置名称标签
                let labelContent = location.name;
                if (location.timestamp) {
                    const date = new Date(location.timestamp * 1000);
                    labelContent += `<br><span style="font-size:12px;">${date.toLocaleString()}</span>`;
                }
                
                L.tooltip({
                    permanent: true,
                    direction: 'top',
                    className: 'location-label',
                    offset: [0, -10]
                })
                .setContent(labelContent)
                .setLatLng(latlng)
                .addTo(map);
                
                // 创建点击时显示的信息窗口
                if (location.details || location.timestamp) {
                    let popupContent = `
                        <div style="min-width:200px;">
                            <h3>${location.name}</h3>
                            <p>纬度: ${location.lat}</p>
                            <p>经度: ${location.lng}</p>
                    `;
                    
                    if (location.timestamp) {
                        const date = new Date(location.timestamp * 1000);
                        popupContent += `<p>时间: ${date.toLocaleString()}</p>`;
                    }
                    
                    if (location.details) {
                        popupContent += `<p>${location.details}</p>`;
                    }
                    
                    popupContent += '</div>';
                    
                    marker.bindPopup(popupContent);
                }
                
                // 如果需要显示红色方框
                if (location.showRedBox) {
                    const sw = L.latLng(location.lat - 0.005, location.lng - 0.005);
                    const ne = L.latLng(location.lat + 0.005, location.lng + 0.005);
                    const boxBounds = L.latLngBounds(sw, ne);
                    
                    L.rectangle(boxBounds, {
                        color: '#FF0000',
                        weight: 2,
                        fillColor: '#FF0000',
                        fillOpacity: 0.1
                    }).addTo(map);
                }
            });
            
            // 绘制蓝色连接线
            L.polyline(polylinePoints, {
                color: '#0066FF',
                weight: 3,
                opacity: 1
            }).addTo(map);
            
            // 调整视图以显示所有点
            map.fitBounds(bounds, {
                padding: [30, 30]
            });
            
            // 如果只有一个点，设置合适的缩放级别
            if (locations.length === 1) {
                map.setView(polylinePoints[0], 15);
            }
        </script>
    </body>
    </html>
    