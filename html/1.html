<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>Leaflet 轨迹图 + 热力图 示例</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- Leaflet 核心 CSS -->
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
  <!-- Leaflet 热力图插件 -->
  <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
  <style>
    #map { height: 100vh; }
  </style>
</head>
<body>
  <div id="map"></div>

  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
  <script>
    // 初始化地图，设置中心点和缩放级别
    const map = L.map('map').setView([39.9042, 116.4074], 13); // 北京

    // 添加 OpenStreetMap 瓦片图层
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; OpenStreetMap contributors'
    }).addTo(map);

    // // ✅ 轨迹图：绘制一条折线
    // const trackPoints = [
    //   [39.9042, 116.4074], // 起点
    //   [39.9123, 116.4125],
    //   [39.9200, 116.4200], // 中间点
    //   [39.9250, 116.4300]  // 终点
    // ];
    // L.polyline(trackPoints, { color: 'blue' }).addTo(map);

    // ✅ 热力图：使用点数据生成热力图
    const heatData = [
      [39.9042, 116.4074, 1],  // [纬度, 经度, 强度]
      [39.9123, 116.4125, 2],
      [39.9200, 116.4200, 3],
      [39.9250, 116.4300, 2],
      [39.9000, 116.4000, 1]
    ];
    const heatLayer = L.heatLayer(heatData, {
      radius: 20,
      blur: 15,
      maxZoom: 17
    }).addTo(map);
  </script>
</body>
</html>
