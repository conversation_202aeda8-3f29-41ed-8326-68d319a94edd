
<!DOCTYPE html>
<html>
<head>
	<title>Heatmap</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
	<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
	<script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
	<style>
		html, body, #map { height: 100%; width: 100%; margin: 0; padding: 0; }
	</style>
</head>
<body>
<div id="map"></div>
<script>
	var map = L.map('map').setView([ 39.904031 ,  116.407521 ],  12 );

	<PERSON><PERSON>tileLayer('https:\/\/mt1.google.com\/vt\/lyrs=m\u0026x={x}\u0026y={y}\u0026z={z}', {
		attribution: 'Google Maps'
	}).addTo(map);

	var heatPoints = [[39.904031,116.407521],[39.912345,116.412225],[39.908002,116.40502],[39.904039,116.407526],[39.904032,116.407522],[39.912345,116.412222],[39.908001,116.40504],[39.904031,116.407525],[39.904033,116.407523],[39.912345,116.412242],[39.908004,116.40505],[39.90403,116.407529],[39.904034,116.407524],[39.912345,116.412262],[39.908005,116.40501],[39.904032,116.407526],[39.904035,116.407525],[39.912346,116.412225],[39.908006,116.40506],[39.904033,116.407521],[39.904036,116.407526],[39.912346,116.412222],[39.908008,116.40504],[39.904035,116.407526]]; 

	L.heatLayer(heatPoints, {
		radius: 25, 
		blur: 15,   
		maxZoom: 18 
	}).addTo(map);

	
	document.body.setAttribute('data-map-ready', 'true');

</script>
</body>
</html>
