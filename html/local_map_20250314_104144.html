
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>轨迹地图</title>
        <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY"></script>
        <style>
            #map {
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            // 位置数据
            const locations = [{"name": "\u5c45\u4f4f\u5730", "lat": 25.0077, "lng": 121.5372, "showRedBox": true, "details": "\u8fd9\u662f\u5c45\u4f4f\u5730\u7684\u8be6\u7ec6\u4fe1\u606f", "timestamp": 1624240800}, {"name": "\u4e2d\u6b63\u8425\u533a", "lat": 25.0419, "lng": 121.5258, "details": "\u8fd9\u662f\u4e2d\u6b63\u8425\u533a\u7684\u8be6\u7ec6\u4fe1\u606f", "timestamp": 1624248000}, {"name": "\u5b66\u6821", "lat": 25.0426, "lng": 121.5347, "details": "\u8fd9\u662f\u5b66\u6821\u7684\u8be6\u7ec6\u4fe1\u606f", "timestamp": 1624255200}];
            
            function initMap() {
                const bounds = new google.maps.LatLngBounds();
                const map = new google.maps.Map(document.getElementById('map'));
                const markers = [];
                const infoWindows = [];
                
                // 为每个位置创建标记
                locations.forEach((location, index) => {
                    const position = new google.maps.LatLng(location.lat, location.lng);
                    bounds.extend(position);
                    
                    // 创建标记
                    const marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: location.name,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            fillColor: '#0066FF',
                            fillOpacity: 1,
                            strokeColor: '#FFFFFF',
                            strokeWeight: 2,
                            scale: 10
                        },
                        zIndex: 1000 - index
                    });
                    
                    markers.push(marker);
                    
                    // 创建标签
                    let labelContent = location.name;
                    if (location.timestamp) {
                        const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                        labelContent += `<br><span style="font-size:12px;">${timeStr}</span>`;
                    }
                    
                    const label = new google.maps.InfoWindow({
                        content: `<div style="color:red; font-weight:bold; font-size:16px;">${labelContent}</div>`,
                        position: position,
                        disableAutoPan: true
                    });
                    
                    label.open(map);
                    
                    // 创建信息窗口
                    if (location.details || location.timestamp) {
                        let infoContent = `
                            <div style="min-width:200px;">
                                <h3>${location.name}</h3>
                                <p>纬度: ${location.lat}</p>
                                <p>经度: ${location.lng}</p>
                        `;
                        
                        if (location.timestamp) {
                            const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                            infoContent += `<p>时间: ${timeStr}</p>`;
                        }
                        
                        if (location.details) {
                            infoContent += `<p>${location.details}</p>`;
                        }
                        
                        infoContent += '</div>';
                        
                        const infoWindow = new google.maps.InfoWindow({
                            content: infoContent
                        });
                        
                        infoWindows.push(infoWindow);
                        
                        marker.addListener('click', () => {
                            infoWindows.forEach(window => window.close());
                            infoWindow.open(map, marker);
                        });
                    }
                });
                
                // 连接各点的直线
                let pathCoordinates = locations.map(loc => ({
                    lat: loc.lat, 
                    lng: loc.lng
                }));
                
                new google.maps.Polyline({
                    path: pathCoordinates,
                    geodesic: true,
                    strokeColor: '#0066FF',
                    strokeOpacity: 1.0,
                    strokeWeight: 3,
                    map: map
                });
                
                // 调整视图以显示所有标记
                map.fitBounds(bounds);
                
                // 添加红色方框
                locations.forEach(location => {
                    if (location.showRedBox) {
                        const sw = new google.maps.LatLng(location.lat - 0.005, location.lng - 0.005);
                        const ne = new google.maps.LatLng(location.lat + 0.005, location.lng + 0.005);
                        const boxBounds = new google.maps.LatLngBounds(sw, ne);
                        
                        new google.maps.Rectangle({
                            bounds: boxBounds,
                            strokeColor: '#FF0000',
                            strokeOpacity: 1.0,
                            strokeWeight: 2,
                            fillColor: '#FF0000',
                            fillOpacity: 0.1,
                            map: map
                        });
                    }
                });
            }
            
            // 初始化地图
            window.onload = initMap;
        </script>
    </body>
    </html>
    