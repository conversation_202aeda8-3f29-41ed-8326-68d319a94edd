<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>ECharts 轨迹图 + 热力图 示例</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <!-- 引入 ECharts 核心库 -->
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
  <!-- 引入 ECharts 的地图数据 (这里使用中国地图) -->
  <!-- 注意：实际项目中你可能需要根据需求引入不同的地图数据或使用在线地图服务 -->
  <script src="https://cdn.jsdelivr.net/npm/echarts/map/js/china.js"></script>
  <style>
    /* 确保图表容器有明确的宽度和高度 */
    #main {
      width: 100%;
      height: 100vh; /* 使图表占据整个视口高度 */
    }
  </style>
</head>
<body>
  <!-- 图表容器 -->
  <div id="main"></div>

  <script>
    // 1. 初始化 ECharts 实例
    // 基于准备好的 DOM，初始化 ECharts 实例
    const myChart = echarts.init(document.getElementById('main'));

    // 2. 定义图表配置项
    const option = {
      // backgroundColor: '#404a59', // 可以设置背景色
      title: {
        text: '模拟迁徙 - 轨迹图与热力图',
        subtext: '数据纯属虚构',
        left: 'center',
        textStyle: {
          color: '#333' // 标题颜色
        }
      },
      tooltip: { // 提示框组件
        trigger: 'item', // 数据项图形触发
        formatter: function (params) {
          if (params.seriesType === 'lines') {
            // 轨迹图提示
            return params.data.fromName + ' > ' + params.data.toName;
          } else if (params.seriesType === 'heatmap') {
            // 热力图提示
            // params.value 包含 [经度, 纬度, 热度值]
            return '热度: ' + params.value[2];
          } else if (params.seriesType === 'effectScatter') {
            // 可以为城市点添加提示
             return params.name + ' : ' + params.value[2];
          }
          return '';
        }
      },
      // 地理坐标系组件，用于地图的绘制
      geo: {
        map: 'china', // 使用注册的中国地图
        roam: true,   // 开启鼠标缩放和平移漫游
        label: {
          // normal 状态下的标签样式 (合并过来的配置)
          normal: {
              show: true, // 默认显示标签（省份名称）
              textStyle: {
                  color: '#666', // 标签颜色
                  fontSize: 10 // 标签字号
              }
          },
          emphasis: { // 高亮状态下的标签样式
            // show: false // 原配置，可以保留或改为 true
            show: true // 高亮时也显示省份名称 (合并过来的配置)
          }
        },
        itemStyle: { // 地图区域的多边形 图形样式
          normal: { // 默认样式
            areaColor: '#f3f3f3', // 地图区域的颜色
            borderColor: '#aaa' // 图形的描边颜色
          },
          emphasis: { // 高亮状态下的样式
            areaColor: '#d4d4d4' // 鼠标悬浮时的颜色
          }
        }
      },
      // 系列列表，每个系列通过 type 决定自己的图表类型
      series: [
        // --- 热力图系列 ---
        {
          name: '热力图',
          type: 'heatmap',
          coordinateSystem: 'geo', // 使用地理坐标系
          data: [
            // 数据格式：[经度, 纬度, 热度值]
            [116.4074, 39.9042, 100], // 北京
            [121.4737, 31.2304, 85],  // 上海
            [113.2644, 23.1291, 70],  // 广州
            [106.5515, 29.5630, 60],  // 重庆
            [104.0668, 30.5728, 50],  // 成都
            [114.3054, 30.5929, 40],  // 武汉
            [117.2000, 39.1333, 30]   // 天津
          ],
          // 热力图配置
          pointSize: 15, // 热力图点的尺寸
          blurSize: 20   // 热力图模糊大小
        },
        // --- 轨迹图系列 ---
        {
          name: '轨迹线',
          type: 'lines',
          coordinateSystem: 'geo', // 使用地理坐标系
          zlevel: 2, // zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面
          effect: { // 线特效的配置
            show: true,
            period: 6, // 特效动画的时间，单位为 s
            trailLength: 0.1, // 特效尾迹的长度。取值范围 0 到 1，数值越大尾迹越长
            color: '#fff', // 特效标记的颜色
            symbol: 'arrow', // 箭头图标
            symbolSize: 5 // 特效标记的大小
          },
          lineStyle: { // 线条样式
            normal: {
              color: '#46bee9', // 线条颜色
              width: 1, // 线宽
              opacity: 0.6, // 图形透明度
              curveness: 0.2 // 边的曲度，支持从 0 到 1 的值，值越大曲度越大
            }
          },
          data: [
            // 数据格式：{ fromName, toName, coords: [[起点经纬度], [终点经纬度]] }
            { fromName: '北京', toName: '上海', coords: [[116.4074, 39.9042], [121.4737, 31.2304]] },
            { fromName: '北京', toName: '广州', coords: [[116.4074, 39.9042], [113.2644, 23.1291]] },
            { fromName: '北京', toName: '重庆', coords: [[116.4074, 39.9042], [106.5515, 29.5630]] },
            { fromName: '北京', toName: '成都', coords: [[116.4074, 39.9042], [104.0668, 30.5728]] },
            { fromName: '北京', toName: '武汉', coords: [[116.4074, 39.9042], [114.3054, 30.5929]] },
            { fromName: '北京', toName: '天津', coords: [[116.4074, 39.9042], [117.2000, 39.1333]] }
          ]
        },
        // --- (可选) 轨迹图起点/终点标记 ---
        // 可以使用 scatter 或 effectScatter 类型来标记起点和终点
        {
            name: '城市点',
            type: 'effectScatter', // 带涟漪效果的散点图
            coordinateSystem: 'geo',
            data: [ // 数据格式：{ name: '城市名', value: [经度, 纬度, 其他值(可选)] }
                { name: '北京', value: [116.4074, 39.9042, 100] },
                { name: '上海', value: [121.4737, 31.2304, 85] },
                { name: '广州', value: [113.2644, 23.1291, 70] },
                { name: '重庆', value: [106.5515, 29.5630, 60] },
                { name: '成都', value: [104.0668, 30.5728, 50] },
                { name: '武汉', value: [114.3054, 30.5929, 40] },
                { name: '天津', value: [117.2000, 39.1333, 30] }
            ],
            symbolSize: function (val) { // 根据第三个值调整点的大小
                return Math.max(val[2] / 10, 5); // 避免点太小
            },
            label: { // 图形上的文本标签
                formatter: '{b}', // 标签内容格式器，{b} 表示数据名称
                position: 'right', // 标签位置
                show: true // 修改为 true，默认显示标签
            },
            emphasis: { // 高亮状态
                label: {
                    show: true // 鼠标悬浮时显示标签
                }
            },
            itemStyle: { // 图形样式
                 color: '#46bee9' // 点的颜色
            }
        }
      ],
      // 视觉映射组件，用于热力图的颜色映射
      visualMap: {
        min: 0, // 最小值
        max: 100, // 最大值 (根据你的热力图数据调整)
        calculable: true, // 是否显示拖拽用的手柄（手柄能实时拖拽调整颜色范围）
        inRange: { // 定义 在选中范围中 的视觉元素
          // 颜色列表，可以设置多个颜色进行渐变
          color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
        },
        text: ['高', '低'], // 两端的文本
        left: 'left', // 组件离容器左侧的距离
        bottom: '10%', // 组件离容器下侧的距离
        textStyle: {
          color: '#333'
        },
        seriesIndex: [0] // 指定这个 visualMap 控制的是第一个 series（即热力图）
      }
    };

    // 3. 使用刚指定的配置项和数据显示图表。
    myChart.setOption(option);

    // 4. (可选) 添加窗口大小调整监听，使图表自适应
    window.addEventListener('resize', function() {
      myChart.resize();
    });
  </script>
</body>
</html>
// 引入高德地图 JS API (需要替换您的 Key)
// <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=YOUR_AMAP_KEY"></script>
// 引入 ECharts AMap 扩展
// <script src="https://cdn.jsdelivr.net/npm/echarts-extension-amap/dist/echarts-extension-amap.min.js"></script>

const option = {
  // 增加 amap 配置块，用于设置高德地图底图的样式、中心点、缩放级别等
  amap: {
    center: [116.4074, 39.9042], // 地图中心点
    zoom: 10, // 缩放级别
    viewMode: '2D', // 使用 2D 模式
    mapStyle: 'amap://styles/whitesmoke', // 地图样式
    // ... 其他高德地图配置
  },
  series: [
    {
      name: '热力图',
      type: 'heatmap',
      // *** 关键：将坐标系改为 amap ***
      coordinateSystem: 'amap',
      data: [ /* ... 您的热力图数据 ... */ ],
      // ... 热力图其他配置
    },
    {
      name: '轨迹线',
      type: 'lines',
      // *** 关键：将坐标系改为 amap ***
      coordinateSystem: 'amap',
      data: [ /* ... 您的轨迹线数据 ... */ ],
      // ... 轨迹线其他配置
    },
    {
      name: '城市点',
      type: 'effectScatter',
      // *** 关键：将坐标系改为 amap ***
      coordinateSystem: 'amap',
      data: [ /* ... 您的城市点数据 ... */ ],
      // ... 散点图其他配置
    }
  ]
  // ... 其他 ECharts 配置 (如 title, tooltip, visualMap)
};

// 初始化 ECharts 实例
const myChart = echarts.init(document.getElementById('main'));
// 设置配置项
myChart.setOption(option);

// 获取 ECharts 绑定的 AMap 实例 (如果需要直接操作地图)
// const amap = myChart.getModel().getComponent('amap').getAMap();
