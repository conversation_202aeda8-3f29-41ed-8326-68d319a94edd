<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    
        <script>
            L_NO_TOUCH = false;
            L_DISABLE_3D = false;
        </script>
    
    <style>html, body {width: 100%;height: 100%;margin: 0;padding: 0;}</style>
    <style>#map {position:absolute;top:0;bottom:0;right:0;left:0;}</style>
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_1173cef2b0d065851dd6205b7ce35adc {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>
        
</head>
<body>
    
    
            <div class="folium-map" id="map_1173cef2b0d065851dd6205b7ce35adc" ></div>
        
</body>
<script>
    
    
            var map_1173cef2b0d065851dd6205b7ce35adc = L.map(
                "map_1173cef2b0d065851dd6205b7ce35adc",
                {
                    center: [36.0, -117.0],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 6,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_d9cffe136132f2c0e6fda227e442cbea = L.tileLayer(
                "https://tile.openstreetmap.org/{z}/{x}/{y}.png",
                {
  "minZoom": 0,
  "maxZoom": 19,
  "maxNativeZoom": 19,
  "noWrap": false,
  "attribution": "\u0026copy; \u003ca href=\"https://www.openstreetmap.org/copyright\"\u003eOpenStreetMap\u003c/a\u003e contributors",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_d9cffe136132f2c0e6fda227e442cbea.addTo(map_1173cef2b0d065851dd6205b7ce35adc);
        
    
            var marker_a5705b200c2f710e6f1b54a7196b9762 = L.marker(
                [34.0522, -118.2437],
                {
}
            ).addTo(map_1173cef2b0d065851dd6205b7ce35adc);
        
    
            var marker_27a5e42a342a7dbc798e845e04ff8713 = L.marker(
                [36.1699, -115.1398],
                {
}
            ).addTo(map_1173cef2b0d065851dd6205b7ce35adc);
        
    
            var marker_c5e0c55999e283c77bf7024deb63a653 = L.marker(
                [37.7749, -122.4194],
                {
}
            ).addTo(map_1173cef2b0d065851dd6205b7ce35adc);
        
    
            var poly_line_655385584a54a3517832da0d8c371069 = L.polyline(
                [[34.0522, -118.2437], [36.1699, -115.1398], [37.7749, -122.4194]],
                {"bubblingMouseEvents": true, "color": "red", "dashArray": null, "dashOffset": null, "fill": false, "fillColor": "red", "fillOpacity": 0.2, "fillRule": "evenodd", "lineCap": "round", "lineJoin": "round", "noClip": false, "opacity": 0.8, "smoothFactor": 1.0, "stroke": true, "weight": 3}
            ).addTo(map_1173cef2b0d065851dd6205b7ce35adc);
        
</script>
</html>