import os
import asyncio
import folium
from playwright.async_api import async_playwright

# 步骤1：使用 Folium 生成地图 HTML 文件
def generate_map_html(html_filename: str):
    # 定义多个地点的坐标（纬度，经度）
    locations = [
        (34.0522, -118.2437),  # 洛杉矶
        (36.1699, -115.1398),  # 拉斯维加斯
        (37.7749, -122.4194)   # 旧金山
    ]

    # 创建以一个中心点为中心的地图
    m = folium.Map(location=[36.0, -117.0], zoom_start=6)

    # 添加标记
    for loc in locations:
        folium.Marker(location=loc).add_to(m)

    # 绘制连接这些地点的折线（Polyline）
    folium.PolyLine(locations, color='red', weight=3, opacity=0.8).add_to(m)

    # 保存 HTML 文件
    m.save(html_filename)
    print(f"Map HTML 文件已生成：{html_filename}")

# 步骤2：使用 Playwright 打开 HTML 文件并截图
async def screenshot_html(html_path: str, screenshot_path: str):
    # 将 html_path 处理为绝对路径，并加上 file:// 协议
    absolute_html_path = os.path.abspath(html_path)
    if os.name == "nt":
        url = f"file:///{absolute_html_path}"
    else:
        url = f"file://{absolute_html_path}"
    print(f"加载页面：{url}")

    async with async_playwright() as p:
        # 启动浏览器，默认使用 headless 模式
        browser = await p.chromium.launch()
        context = await browser.new_context(
            viewport={"width": 1280, "height": 720}  # 根据需要调整截图尺寸
        )
        page = await context.new_page()
        await page.goto(url)
        # 根据页面情况等待加载，这里等待2秒，实际可根据需要修改等待条件
        await page.wait_for_timeout(2000)
        # 截图并保存为图片文件
        await page.screenshot(path=screenshot_path)
        print(f"截图已保存为：{screenshot_path}")
        await browser.close()

if __name__ == "__main__":
    # 定义 HTML 文件和截图文件名
    html_file = "map_route.html"
    screenshot_file = "map_route.png"

    # 生成地图 HTML 文件
    generate_map_html(html_file)

    # 使用 Playwright 截图生成的 HTML 页面
    asyncio.run(screenshot_html(html_file, screenshot_file))

    asyncio.run(screenshot_html(html_file, screenshot_file))
    print("程序执行结束")
