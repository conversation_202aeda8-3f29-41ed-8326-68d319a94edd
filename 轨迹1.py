from playwright.sync_api import sync_playwright
import time
import os
from datetime import datetime
import json

def draw_custom_trajectory_on_map(locations, save_screenshot=True, screenshot_path=None, browser_type="chrome"):
    """
    在本地HTML地图上创建自定义标记和蓝色连接线
    
    :param locations: 位置列表，每个位置是一个字典，包含name、lat、lng、timestamp属性
    :param save_screenshot: 是否保存截图，默认为True
    :param screenshot_path: 截图保存路径，默认为None（将保存在当前目录）
    :param browser_type: 浏览器类型，可选值为"chrome"、"firefox"、"webkit"，默认为"chrome"
    :return: 截图保存路径
    """
    # 首先按时间戳对位置进行排序
    if all('timestamp' in loc for loc in locations):
        locations = sorted(locations, key=lambda x: x['timestamp'])
    
    if screenshot_path is None:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"map_trajectory_{current_time}.png"
    
    # 创建本地HTML文件
    html_file = f"local_map_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    # 生成HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>轨迹地图</title>
        <style>
            #map {{
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }}
            html, body {{
                height: 100%;
                margin: 0;
                padding: 0;
            }}
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            // 位置数据
            const locations = {json.dumps(locations)};
            
            function initMap() {{
                // 创建地图
                const map = new google.maps.Map(document.getElementById('map'), {{
                    zoom: 12,
                    center: {{ lat: {locations[0]['lat']}, lng: {locations[0]['lng']} }}
                }});
                
                const bounds = new google.maps.LatLngBounds();
                const markers = [];
                const infoWindows = [];
                
                // 为每个位置创建标记
                locations.forEach((location, index) => {{
                    const position = new google.maps.LatLng(location.lat, location.lng);
                    bounds.extend(position);
                    
                    // 创建标记
                    const marker = new google.maps.Marker({{
                        position: position,
                        map: map,
                        title: location.name,
                        icon: {{
                            path: google.maps.SymbolPath.CIRCLE,
                            fillColor: '#0066FF',
                            fillOpacity: 1,
                            strokeColor: '#FFFFFF',
                            strokeWeight: 2,
                            scale: 10
                        }},
                        zIndex: 1000 - index
                    }});
                    
                    markers.push(marker);
                    
                    // 创建标签
                    let labelContent = location.name;
                    if (location.timestamp) {{
                        const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                        labelContent += `<br><span style="font-size:12px;">${{timeStr}}</span>`;
                    }}
                    
                    const label = new google.maps.InfoWindow({{
                        content: `<div style="color:red; font-weight:bold; font-size:16px;">${{labelContent}}</div>`,
                        position: position,
                        disableAutoPan: true
                    }});
                    
                    label.open(map);
                    
                    // 创建信息窗口
                    if (location.details || location.timestamp) {{
                        let infoContent = `
                            <div style="min-width:200px;">
                                <h3>${{location.name}}</h3>
                                <p>纬度: ${{location.lat}}</p>
                                <p>经度: ${{location.lng}}</p>
                        `;
                        
                        if (location.timestamp) {{
                            const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                            infoContent += `<p>时间: ${{timeStr}}</p>`;
                        }}
                        
                        if (location.details) {{
                            infoContent += `<p>${{location.details}}</p>`;
                        }}
                        
                        infoContent += '</div>';
                        
                        const infoWindow = new google.maps.InfoWindow({{
                            content: infoContent
                        }});
                        
                        infoWindows.push(infoWindow);
                        
                        marker.addListener('click', () => {{
                            infoWindows.forEach(window => window.close());
                            infoWindow.open(map, marker);
                        }});
                    }}
                }});
                
                // 连接各点的直线
                let pathCoordinates = locations.map(loc => ({{
                    lat: loc.lat, 
                    lng: loc.lng
                }}));
                
                const polyline = new google.maps.Polyline({{
                    path: pathCoordinates,
                    geodesic: true,
                    strokeColor: '#0066FF',
                    strokeOpacity: 1.0,
                    strokeWeight: 3,
                    map: map
                }});
                
                // 调整视图以显示所有标记
                map.fitBounds(bounds);
                
                // 如果只有一个位置，设置适当的缩放级别
                if (locations.length === 1) {{
                    map.setZoom(15);
                }}
            }}
        </script>
        <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI&language=zh-CN&region=CN&callback=initMap" async defer></script>
    </body>
    </html>
    """
    
    # 保存HTML文件
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"已创建本地地图文件: {html_file}")
    
    # 使用浏览器打开HTML文件
    saved_path = None
    with sync_playwright() as p:
        # 根据指定的浏览器类型启动浏览器
        print(f"正在启动 {browser_type} 浏览器...")
        if browser_type == "chrome":
            browser = p.chromium.launch(headless=True)  # 改为headless模式以便截图
        elif browser_type == "firefox":
            browser = p.firefox.launch(headless=True)
        elif browser_type == "webkit":
            browser = p.webkit.launch(headless=True)
        
        context = browser.new_context(viewport={"width": 1920, "height": 1080})
        page = context.new_page()
        
        # 启用浏览器控制台日志
        page.on("console", lambda msg: print(f"浏览器控制台: {msg.text}"))
        
        try:
            # 打开本地HTML文件
            file_url = f"file://{os.path.abspath(html_file)}"
            print(f"正在打开本地地图文件: {file_url}")
            page.goto(file_url, timeout=60000)
            
            # 等待地图加载
            print("等待地图加载...")
            page.wait_for_load_state("networkidle")

            # 等待Google Maps API加载完成
            page.wait_for_function("typeof google !== 'undefined' && typeof google.maps !== 'undefined'")
            time.sleep(8)  # 等待地图渲染完成

            # 保存截图
            if save_screenshot:
                print("准备保存截图...")
                page.screenshot(path=screenshot_path, full_page=False)
                saved_path = os.path.abspath(screenshot_path)
                print(f"地图轨迹已保存为图片：{saved_path}")

        except Exception as e:
            print(f"脚本执行过程中出错: {str(e)}")

        finally:
            # 关闭浏览器
            browser.close()
    
    return saved_path

if __name__ == "__main__":
    # 使用更明显的位置差异进行测试
    test_locations = [
        {
            "name": "台北101",
            "lat": 25.0338,
            "lng": 121.5646,
            "timestamp": 1624240800
        },
        {
            "name": "台北车站",
            "lat": 25.0478,
            "lng": 121.5170,
            "timestamp": 1624248000
        },
        {
            "name": "国立台湾大学",
            "lat": 25.0174,
            "lng": 121.5399,
            "timestamp": 1624255200
        }
    ]
    
    # 使用测试位置
    draw_custom_trajectory_on_map(test_locations, save_screenshot=True, browser_type="chrome")
