from playwright.sync_api import sync_playwright
import time
import os
from datetime import datetime
import json
def draw_custom_trajectory_on_map(locations, bing_maps_key, save_screenshot=True, screenshot_path=None, browser_type="chrome"):
    """
    在本地HTML地图上创建自定义标记和蓝色连接线
    
    :param locations: 位置列表，每个位置是一个字典，包含name、lat、lng、timestamp属性
    :param bing_maps_key: Bing Maps API密钥
    :param save_screenshot: 是否保存截图，默认为True
    :param screenshot_path: 截图保存路径，默认为None（将保存在当前目录）
    :param browser_type: 浏览器类型，可选值为"chrome"、"firefox"、"webkit"，默认为"chrome"
    :return: 截图保存路径
    """
    if screenshot_path is None:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"map_trajectory_{current_time}.png"
    
    html_file = f"local_map_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    # 生成HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>轨迹地图</title>
        <script type='text/javascript' src='https://www.bing.com/api/maps/mapcontrol?key={bing_maps_key}'></script>
        <style>
            #map {{
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }}
            html, body {{
                height: 100%;
                margin: 0;
                padding: 0;
            }}
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            // 位置数据
            const locations = {json.dumps(locations)};
            
            function loadMapScenario() {{
                // 创建地图
                const map = new Microsoft.Maps.Map(document.getElementById('map'), {{
                    credentials: '{bing_maps_key}',
                    center: new Microsoft.Maps.Location(locations[0].lat, locations[0].lng),
                    zoom: 12,
                    mapTypeId: Microsoft.Maps.MapTypeId.road
                }});
                
                const bounds = new Microsoft.Maps.LocationRect.fromLocations([]);
                const markers = [];
                const infoboxes = [];
                
                // 为每个位置创建标记
                locations.forEach((location, index) => {{
                    const position = new Microsoft.Maps.Location(location.lat, location.lng);
                    bounds.locations.push(position);
                    
                    // 创建标记
                    const marker = new Microsoft.Maps.Pushpin(position, {{
                        color: '#0066FF',
                        size: 12
                    }});
                    
                    markers.push(marker);
                    map.entities.push(marker);
                    
                    // 创建标签
                    let labelContent = location.name;
                    if (location.timestamp) {{
                        const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                        labelContent += `\\n${{timeStr}}`;
                    }}
                    
                    // 创建信息窗口
                    if (location.details || location.timestamp) {{
                        let infoContent = `
                            <div style="min-width:200px;">
                                <h3>${{location.name}}</h3>
                                <p>纬度: ${{location.lat}}</p>
                                <p>经度: ${{location.lng}}</p>
                        `;
                        
                        if (location.timestamp) {{
                            const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                            infoContent += `<p>时间: ${{timeStr}}</p>`;
                        }}
                        
                        if (location.details) {{
                            infoContent += `<p>${{location.details}}</p>`;
                        }}
                        
                        infoContent += '</div>';
                        
                        const infobox = new Microsoft.Maps.Infobox(position, {{
                            title: location.name,
                            description: infoContent,
                            visible: false
                        }});
                        
                        infoboxes.push(infobox);
                        map.entities.push(infobox);
                        
                        Microsoft.Maps.Events.addHandler(marker, 'click', () => {{
                            infoboxes.forEach(box => box.setOptions({{ visible: false }}));
                            infobox.setOptions({{ visible: true }});
                        }});
                    }}
                    
                    // 添加永久标签
                    const label = new Microsoft.Maps.Infobox(position, {{
                        title: labelContent,
                        visible: true,
                        showCloseButton: false,
                        showPointer: false,
                        offset: new Microsoft.Maps.Point(0, -20)
                    }});
                    map.entities.push(label);
                }});
                
                // 连接各点的直线
                const lineLocations = locations.map(loc => 
                    new Microsoft.Maps.Location(loc.lat, loc.lng)
                );
                
                const polyline = new Microsoft.Maps.Polyline(lineLocations, {{
                    strokeColor: '#0066FF',
                    strokeThickness: 3
                }});
                
                map.entities.push(polyline);
                
                // 调整视图以显示所有标记
                if (locations.length > 1) {{
                    map.setView({{ bounds: Microsoft.Maps.LocationRect.fromLocations(lineLocations) }});
                }} else if (locations.length === 1) {{
                    map.setView({{ center: lineLocations[0], zoom: 15 }});
                }}
            }}
        </script>
    </body>
    </html>
    """
    
    # 保存HTML文件
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"已创建本地地图文件: {html_file}")
    
    # 使用浏览器打开HTML文件
    saved_path = None
    with sync_playwright() as p:
        print(f"正在启动 {browser_type} 浏览器...")
        if browser_type == "chrome":
            browser = p.chromium.launch(headless=False, channel="chrome")
        elif browser_type == "firefox":
            browser = p.firefox.launch(headless=False)
        elif browser_type == "webkit":
            browser = p.webkit.launch(headless=False)
        
        context = browser.new_context(viewport={"width": 1920, "height": 1080})
        page = context.new_page()
        
        page.on("console", lambda msg: print(f"浏览器控制台: {msg.text}"))
        
        try:
            file_url = f"file://{os.path.abspath(html_file)}"
            print(f"正在打开本地地图文件: {file_url}")
            page.goto(file_url, timeout=60000)
            
            print("等待地图加载...")
            page.wait_for_load_state("networkidle")
            time.sleep(5)  # 等待地图渲染
            
            if save_screenshot:
                print("准备保存截图...")
                saved_path = page.screenshot(path=screenshot_path, full_page=False)
                print(f"地图轨迹已保存为图片：{os.path.abspath(screenshot_path)}")
        
        except Exception as e:
            print(f"脚本执行过程中出错: {str(e)}")
        
        print("已在地图上绘制轨迹，请手动关闭浏览器窗口结束程序")
        page.wait_for_event("close")
    
    return saved_path

if __name__ == "__main__":
    # 使用更明显的位置差异进行测试
    test_locations = [
        {
            "name": "台北101",
            "lat": 25.0338,
            "lng": 121.5646,
            "timestamp": 1624240800
        },
        {
            "name": "台北车站",
            "lat": 25.0478,
            "lng": 121.5170,
            "timestamp": 1624248000
        },
        {
            "name": "国立台湾大学",
            "lat": 25.0174,
            "lng": 121.5399,
            "timestamp": 1624255200
        }
    ]
    
    # 替换为您的Bing Maps API密钥
    bing_maps_key = "YOUR_BING_MAPS_KEY"
    
    # 使用测试位置
    draw_custom_trajectory_on_map(test_locations, bing_maps_key, save_screenshot=True, browser_type="chrome")