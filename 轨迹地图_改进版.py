from playwright.sync_api import sync_playwright
import time
import os
from datetime import datetime
import json

def draw_trajectory_map(locations, api_key="AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI", save_screenshot=True, screenshot_path=None):
    """
    在Google地图上创建轨迹图，支持中文显示
    
    :param locations: 位置列表，每个位置包含name、lat、lng、timestamp属性
    :param api_key: Google Maps API密钥
    :param save_screenshot: 是否保存截图
    :param screenshot_path: 截图保存路径
    :return: 截图保存路径
    """
    # 按时间戳排序
    if all('timestamp' in loc for loc in locations):
        locations = sorted(locations, key=lambda x: x['timestamp'])
    
    if screenshot_path is None:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = f"trajectory_map_{current_time}.png"
    
    # 创建HTML文件
    html_file = f"trajectory_map_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
    
    # 生成HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>轨迹地图</title>
        <style>
            #map {{
                height: 100vh;
                width: 100vw;
            }}
            body {{
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
            }}
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            const locations = {json.dumps(locations)};
            
            function initMap() {{
                const map = new google.maps.Map(document.getElementById('map'), {{
                    zoom: 12,
                    center: {{ lat: {locations[0]['lat']}, lng: {locations[0]['lng']} }},
                    mapTypeId: 'roadmap'
                }});
                
                const bounds = new google.maps.LatLngBounds();
                const markers = [];
                
                // 创建标记
                locations.forEach((location, index) => {{
                    const position = new google.maps.LatLng(location.lat, location.lng);
                    bounds.extend(position);
                    
                    const marker = new google.maps.Marker({{
                        position: position,
                        map: map,
                        title: location.name,
                        label: {{
                            text: (index + 1).toString(),
                            color: 'white',
                            fontWeight: 'bold'
                        }},
                        icon: {{
                            path: google.maps.SymbolPath.CIRCLE,
                            fillColor: index === 0 ? '#00FF00' : (index === locations.length - 1 ? '#FF0000' : '#0066FF'),
                            fillOpacity: 1,
                            strokeColor: '#FFFFFF',
                            strokeWeight: 2,
                            scale: 12
                        }}
                    }});
                    
                    markers.push(marker);
                    
                    // 信息窗口
                    let infoContent = `
                        <div style="min-width:200px;">
                            <h3>${{location.name}}</h3>
                            <p><strong>位置:</strong> ${{location.lat.toFixed(6)}}, ${{location.lng.toFixed(6)}}</p>
                    `;
                    
                    if (location.timestamp) {{
                        const timeStr = new Date(location.timestamp * 1000).toLocaleString('zh-CN');
                        infoContent += `<p><strong>时间:</strong> ${{timeStr}}</p>`;
                    }}
                    
                    if (location.details) {{
                        infoContent += `<p><strong>详情:</strong> ${{location.details}}</p>`;
                    }}
                    
                    infoContent += '</div>';
                    
                    const infoWindow = new google.maps.InfoWindow({{
                        content: infoContent
                    }});
                    
                    marker.addListener('click', () => {{
                        markers.forEach(m => {{
                            if (m.infoWindow) m.infoWindow.close();
                        }});
                        infoWindow.open(map, marker);
                        marker.infoWindow = infoWindow;
                    }});
                }});
                
                // 创建轨迹线
                const pathCoordinates = locations.map(loc => ({{
                    lat: loc.lat, 
                    lng: loc.lng
                }}));
                
                const polyline = new google.maps.Polyline({{
                    path: pathCoordinates,
                    geodesic: true,
                    strokeColor: '#0066FF',
                    strokeOpacity: 1.0,
                    strokeWeight: 4,
                    map: map
                }});
                
                // 调整视图
                if (locations.length > 1) {{
                    map.fitBounds(bounds);
                    const padding = {{ top: 50, right: 50, bottom: 50, left: 50 }};
                    map.fitBounds(bounds, padding);
                }} else {{
                    map.setZoom(15);
                }}
                
                // 标记地图加载完成
                window.mapLoaded = true;
            }}
        </script>
        <script src="https://maps.googleapis.com/maps/api/js?key={api_key}&language=zh-CN&region=CN&callback=initMap" async defer></script>
    </body>
    </html>
    """
    
    # 保存HTML文件
    with open(html_file, "w", encoding="utf-8") as f:
        f.write(html_content)
    
    print(f"已创建地图文件: {html_file}")
    
    # 截图
    saved_path = None
    if save_screenshot:
        with sync_playwright() as p:
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(viewport={"width": 2560, "height": 1600})
            page = context.new_page()
            
            try:
                file_url = f"file://{os.path.abspath(html_file)}"
                print(f"正在加载地图: {file_url}")
                page.goto(file_url, timeout=60000)
                
                # 等待地图加载完成
                page.wait_for_function("window.mapLoaded === true", timeout=30000)
                time.sleep(3)  # 额外等待渲染
                
                print("正在保存截图...")
                page.screenshot(path=screenshot_path, full_page=False)
                saved_path = os.path.abspath(screenshot_path)
                print(f"轨迹地图已保存: {saved_path}")
                
            except Exception as e:
                print(f"截图失败: {str(e)}")
            
            finally:
                browser.close()
    
    return saved_path

if __name__ == "__main__":
    # 测试数据 - 北京景点
    test_locations = [
        {
            "name": "天安门广场",
            "lat": 39.9042,
            "lng": 116.4074,
            "timestamp": 1624240800,
            "details": "中华人民共和国的象征"
        },
        {
            "name": "故宫博物院",
            "lat": 39.9163,
            "lng": 116.3972,
            "timestamp": 1624248000,
            "details": "明清两朝的皇家宫殿"
        },
        {
            "name": "天坛公园",
            "lat": 39.8822,
            "lng": 116.4066,
            "timestamp": 1624255200,
            "details": "明清皇帝祭天的场所"
        },
        {
            "name": "颐和园",
            "lat": 39.9999,
            "lng": 116.2755,
            "timestamp": 1624262400,
            "details": "中国古典园林的杰作"
        }
    ]
    
    # 生成轨迹地图
    draw_trajectory_map(test_locations, save_screenshot=True)
