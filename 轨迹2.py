import json
import os
import time
from datetime import datetime
from playwright.sync_api import sync_playwright

def create_local_map(locations, output_file=None, save_screenshot=True, screenshot_path=None):
    """
    创建本地HTML地图文件展示轨迹（使用Leaflet和OpenStreetMap，无需API密钥）
    
    :param locations: 位置列表
    :param output_file: 输出文件路径
    :param save_screenshot: 是否保存截图
    :param screenshot_path: 截图保存路径，默认为None（将自动生成文件名）
    :return: 生成的HTML文件路径和截图路径的元组
    """
    if output_file is None:
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"local_map_{current_time}.html"
    
    # 按时间戳排序
    if all('timestamp' in loc for loc in locations):
        locations = sorted(locations, key=lambda x: x['timestamp'])
    
    # 创建HTML内容
    html_content = f'''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>轨迹地图</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"/>
        <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
        <style>
            #map {{
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }}
            html, body {{
                height: 100%;
                margin: 0;
                padding: 0;
            }}
            .location-label {{
                background: none;
                border: none;
                box-shadow: none;
                color: red;
                font-weight: bold;
                font-size: 16px;
                text-shadow: 1px 1px 2px white;
            }}
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            // 位置数据
            const locations = {json.dumps(locations)};
            
            // 初始化地图
            const map = L.map('map');
            
            // 添加OpenStreetMap底图
            L.tileLayer('https://{{s}}.tile.openstreetmap.org/{{z}}/{{x}}/{{y}}.png', {{
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }}).addTo(map);
            
            // 创建一个用于绘制轨迹的线条
            const polylinePoints = [];
            const bounds = L.latLngBounds();
            
            // 为每个位置添加标记
            locations.forEach((location, index) => {{
                const latlng = L.latLng(location.lat, location.lng);
                polylinePoints.push(latlng);
                bounds.extend(latlng);
                
                // 创建蓝色圆形标记
                const marker = L.circleMarker(latlng, {{
                    radius: 8,
                    fillColor: '#0066FF',
                    color: '#FFFFFF',
                    weight: 2,
                    opacity: 1,
                    fillOpacity: 1
                }}).addTo(map);
                
                // 添加位置名称标签
                let labelContent = location.name;
                if (location.timestamp) {{
                    const date = new Date(location.timestamp * 1000);
                    labelContent += `<br><span style="font-size:12px;">${{date.toLocaleString()}}</span>`;
                }}
                
                L.tooltip({{
                    permanent: true,
                    direction: 'top',
                    className: 'location-label',
                    offset: [0, -10]
                }})
                .setContent(labelContent)
                .setLatLng(latlng)
                .addTo(map);
                
                // 创建点击时显示的信息窗口
                if (location.details || location.timestamp) {{
                    let popupContent = `
                        <div style="min-width:200px;">
                            <h3>${{location.name}}</h3>
                            <p>纬度: ${{location.lat}}</p>
                            <p>经度: ${{location.lng}}</p>
                    `;
                    
                    if (location.timestamp) {{
                        const date = new Date(location.timestamp * 1000);
                        popupContent += `<p>时间: ${{date.toLocaleString()}}</p>`;
                    }}
                    
                    if (location.details) {{
                        popupContent += `<p>${{location.details}}</p>`;
                    }}
                    
                    popupContent += '</div>';
                    
                    marker.bindPopup(popupContent);
                }}
                
                // 如果需要显示红色方框
                if (location.showRedBox) {{
                    const sw = L.latLng(location.lat - 0.005, location.lng - 0.005);
                    const ne = L.latLng(location.lat + 0.005, location.lng + 0.005);
                    const boxBounds = L.latLngBounds(sw, ne);
                    
                    L.rectangle(boxBounds, {{
                        color: '#FF0000',
                        weight: 2,
                        fillColor: '#FF0000',
                        fillOpacity: 0.1
                    }}).addTo(map);
                }}
            }});
            
            // 绘制蓝色连接线
            L.polyline(polylinePoints, {{
                color: '#0066FF',
                weight: 3,
                opacity: 1
            }}).addTo(map);
            
            // 调整视图以显示所有点
            map.fitBounds(bounds, {{
                padding: [30, 30]
            }});
            
            // 如果只有一个点，设置合适的缩放级别
            if (locations.length === 1) {{
                map.setView(polylinePoints[0], 15);
            }}
            
            // 添加一个标记表示地图已完全加载完成
            document.body.setAttribute('data-map-loaded', 'true');
        </script>
    </body>
    </html>
    '''
    
    # 写入HTML文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"地图已生成: {os.path.abspath(output_file)}")
    
    # 保存截图
    screenshot_file = None
    if save_screenshot:
        if screenshot_path is None:
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"map_screenshot_{current_time}.png"
        
        screenshot_file = capture_map_screenshot(output_file, screenshot_path)
    
    return output_file, screenshot_file

def capture_map_screenshot(html_file_path, screenshot_path):
    """
    使用Playwright打开本地HTML文件并截图
    
    :param html_file_path: HTML文件路径
    :param screenshot_path: 截图保存路径
    :return: 截图保存路径
    """
    abs_path = os.path.abspath(html_file_path)
    file_url = f"file://{abs_path}"
    
    print(f"正在打开地图并截图...")
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)  # 无头模式更适合截图
        page = browser.new_page(viewport={"width": 1920, "height": 1080})
        
        try:
            # 打开本地HTML文件
            page.goto(file_url, wait_until="networkidle")
            
            # 等待地图完全加载
            page.wait_for_selector("[data-map-loaded='true']", timeout=30000)
            
            # 额外等待一些时间确保地图瓦片全部加载
            time.sleep(3)
            
            # 截图
            page.screenshot(path=screenshot_path)
            print(f"地图截图已保存: {os.path.abspath(screenshot_path)}")
        except Exception as e:
            print(f"截图过程中出错: {str(e)}")
        finally:
            browser.close()
    
    return screenshot_path

if __name__ == "__main__":
    # 示例位置数据
    locations = [
        {
            "name": "居住地",
            "lat": 25.0077,
            "lng": 121.5372,
            "showRedBox": True,
            "details": "这是居住地的详细信息",
            "timestamp": 1624240800  # 2021-06-21 08:00:00
        },
        {
            "name": "中正营区",
            "lat": 25.0419,
            "lng": 121.5258,
            "details": "这是中正营区的详细信息",
            "timestamp": 1624248000  # 2021-06-21 10:00:00
        },
        {
            "name": "学校",
            "lat": 25.0426,
            "lng": 121.5347,
            "details": "这是学校的详细信息",
            "timestamp": 1624255200  # 2021-06-21 12:00:00
        }
    ]
    
    # 创建本地地图文件并截图
    html_file, screenshot_file = create_local_map(locations, save_screenshot=True)
    
    # 自动打开生成的HTML文件（可选）
    import webbrowser
    webbrowser.open('file://' + os.path.abspath(html_file))
    
    print("程序执行完成！")
    print(f"- HTML文件: {os.path.abspath(html_file)}")
    print(f"- 截图文件: {os.path.abspath(screenshot_file) if screenshot_file else '未生成'}") 