import pandas as pd
import folium
from folium.plugins import HeatMap
import asyncio
from playwright.async_api import async_playwright
import os

# 示例数据（替换为您的坐标）
data = {
    "lat": [39.904030, 39.912345, 39.908000, 39.904030,
    39.904030, 39.912345, 39.908000, 39.904030,
    39.904030, 39.912345, 39.908000, 39.904030,
    39.904030, 39.912345, 39.908000, 39.904030,
    39.904030, 39.912345, 39.908000, 39.904030,
    39.904030, 39.912345, 39.908000, 39.904030],  # 纬度
    "lon": [116.407526, 116.412222, 116.405000, 116.407526,
    116.407526, 116.412222, 116.405000, 116.407526,
    116.407526, 116.412222, 116.405000, 116.407526,
    116.407526, 116.412222, 116.405000, 116.407526,
    116.407526, 116.412222, 116.405000, 116.407526,
    116.407526, 116.412222, 116.405000, 116.407526],  # 经度
    "time": ["2023-10-01 08:00", "2023-10-01 12:30", "2023-10-01 18:00", "2023-10-01 23:59",
    "2023-10-02 08:00", "2023-10-02 12:30", "2023-10-02 18:00", "2023-10-02 23:59",
    "2023-10-03 08:00", "2023-10-03 12:30", "2023-10-03 18:00", "2023-10-03 23:59",
    "2023-10-04 08:00", "2023-10-04 12:30", "2023-10-04 18:00", "2023-10-04 23:59",
    "2023-10-05 08:00", "2023-10-05 12:30", "2023-10-05 18:00", "2023-10-05 23:59",
    "2023-10-06 08:00", "2023-10-06 12:30", "2023-10-06 18:00", "2023-10-06 23:59"]  # 可选时间戳
}

# 创建Google地图底图
# 方法1：使用Google地图瓦片（推荐）
m = folium.Map(
    location=[data["lat"][0], data["lon"][0]],
    zoom_start=12,
    tiles="https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}",
    attr="Google Maps"
)

# 方法2：使用Google卫星地图
# m = folium.Map(
#     location=[data["lat"][0], data["lon"][0]],
#     zoom_start=12,
#     tiles="https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}",
#     attr="Google Satellite"
# )

# 方法3：使用OpenStreetMap（默认）
# m = folium.Map(
#     location=[data["lat"][0], data["lon"][0]],
#     zoom_start=12
# )

# 生成热力图
HeatMap(data=list(zip(data["lat"], data["lon"]))).add_to(m)

# 保存为HTML文件
m.save("heatmap.html")

# 使用Playwright截图保存为PNG
async def capture_screenshot():
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch()
        page = await browser.new_page(viewport={"width": 1920, "height": 1080})
        
        # 打开HTML文件
        file_path = f"file://{os.path.abspath('heatmap.html')}"
        await page.goto(file_path)
        
        # 等待地图加载完成（等待地图瓦片加载）
        await page.wait_for_load_state("networkidle")
        
        # 额外等待一些时间确保热力图渲染完成
        await page.wait_for_timeout(2000)
        
        # 截图并保存
        await page.screenshot(path="heatmap.png", full_page=True)
        
        # 关闭浏览器
        await browser.close()
        
        print("热力图已保存为PNG图片：heatmap.png")

# 运行截图函数
if __name__ == "__main__":
    asyncio.run(capture_screenshot()) 
