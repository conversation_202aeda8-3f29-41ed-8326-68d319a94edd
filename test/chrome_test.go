package test

import (
	"fmt"
	"testing"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
)

func TestBrowser(t *testing.T) {
	targetURL := "https://www.google.com/maps"

	fmt.Printf("准备以 App Mode 启动浏览器并打开: %s\n", targetURL)

	// 创建启动器并配置浏览器选项
	url := launcher.New().
		Headless(false).                                       // 禁用 headless 模式，确保浏览器可见
		Leakless(true).                                        // 确保浏览器进程正常管理
		NoSandbox(true).                                       // 禁用沙箱模式
		Set("disable-blink-features", "AutomationControlled"). // 避免自动化检测
		Set("window-size", "1920,1080").                       // 设置窗口大小
		Set("app", targetURL).                                 // 在末尾设置 app 模式，确保其他设置生效
		Set("chrome-frame").                                   // 移除浏览器框架
		Set("disable-extensions").                             // 禁用扩展
		Set("disable-features", "TranslateUI").                // 禁用翻译UI
		MustLaunch()
	fmt.Printf("浏览器已启动: %s\n", url)

	// 创建浏览器实例
	browser := rod.New().ControlURL(url).MustConnect()
	// defer browser.MustClose()  // Don't automatically close the browser

	// 创建页面
	page := browser.MustPage(targetURL)

	// 等待页面加载完成
	time.Sleep(5 * time.Second)

	// 截图并保存
	path := "../png/maps_screenshot.png"
	page.MustScreenshot(path)
	fmt.Printf("截图已保存到: %s\n", path)

	// Keep the test running until interrupted
	select {}
}

func TestBrowser1(t *testing.T) {
	url := launcher.NewAppMode("https://www.google.com/maps").
		// Set("app", "https://www.google.com/maps").
		Headless(false).
		// 禁用 AutomationControlled 特征
		Leakless(true).
		NoSandbox(true).
		// 加上这句会自动规避 navigator.webdriver
		// 并模拟真实用户行为
		Set("disable-blink-features", "AutomationControlled").
		MustLaunch()

	browser := rod.New().ControlURL(url).MustConnect()

	// 自动规避检测（关键）
	browser = browser.MustIgnoreCertErrors(true)
	page := browser.MustPage("https://www.google.com/maps")

	page.MustWaitLoad()
	page.MustScreenshot("baidu.png")
}

func TestBrowser2(t *testing.T) {
	url := launcher.New().
		// Set("app", "https://www.google.com/maps").
		Headless(false).
		// 禁用 AutomationControlled 特征
		Leakless(true).
		NoSandbox(true).
		// 加上这句会自动规避 navigator.webdriver
		// 并模拟真实用户行为
		Set("disable-blink-features", "AutomationControlled").
		MustLaunch()

	browser := rod.New().ControlURL(url).MustConnect()

	// 自动规避检测（关键）
	browser = browser.MustIgnoreCertErrors(true)
	page := browser.MustPage("https://www.google.com/maps")

	page.MustWaitLoad()
	page.MustScreenshot("baidu.png")
}
