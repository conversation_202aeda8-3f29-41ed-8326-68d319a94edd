package test

import (
	"context" // 导入 context 包
	"database/sql"
	"log/slog"
	"testing"

	_ "github.com/mattn/go-sqlite3"
)

type SQLiteHandler struct {
	db    *sql.DB
	attrs []slog.Attr // 用于存储 WithAttrs 添加的属性
	group string      // 用于存储 WithGroup 添加的组名
}

func NewSQLiteHandler(db *sql.DB) *SQLiteHandler {
	return &SQLiteHandler{db: db}
}

// Enabled 方法：简单实现，始终返回 true，表示处理所有级别的日志
// 你可以根据需要实现更复杂的逻辑，例如只记录特定级别以上的日志
func (h *SQLiteHandler) Enabled(ctx context.Context, level slog.Level) bool {
	// 这里可以添加逻辑，比如根据配置的最低日志级别判断
	// return level >= slog.LevelInfo // 例如：只记录 Info 及以上级别
	return true
}

// Handle 方法：处理日志记录，将其写入数据库
func (h *SQLiteHandler) Handle(ctx context.Context, r slog.Record) error {
	// 可以将 r.Level, r.Message, r.Time 等信息存入数据库
	// 也可以遍历 r.Attrs 将属性也存入（需要修改数据库表结构或将属性序列化）

	// 基础实现：只记录 Level 和 Message
	_, err := h.db.ExecContext(ctx, "INSERT INTO logs (level, message) VALUES (?, ?)", r.Level.String(), r.Message) // 使用 ExecContext 传递上下文

	// 如果需要记录属性，可以像这样遍历：
	// var attrsStr string
	// r.Attrs(func(a slog.Attr) bool {
	//  attrsStr += fmt.Sprintf(" %s=%s", a.Key, a.Value.String())
	//  return true
	// })
	// _, err := h.db.ExecContext(ctx, "INSERT INTO logs (level, message, attributes) VALUES (?, ?, ?)", r.Level.String(), r.Message, attrsStr)

	return err
}

// WithAttrs 方法：返回一个包含新属性的新 Handler
func (h *SQLiteHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	newHandler := *h // 创建副本
	newHandler.attrs = append(newHandler.attrs, attrs...)

	return &newHandler
	// 注意：上面的简单实现没有在 Handle 中实际使用 attrs。
	// 如果需要在 Handle 中使用这些累积的属性，逻辑会更复杂。
	// 通常需要将 Record 的属性和 Handler 的属性合并。
}

// WithGroup 方法：返回一个将日志记录放入指定组的新 Handler
func (h *SQLiteHandler) WithGroup(name string) slog.Handler {
	newHandler := *h // 创建副本
	if newHandler.group != "" {
		newHandler.group += "." // 如果已有组名，用点连接
	}
	newHandler.group += name
	return &newHandler
	// 注意：上面的简单实现没有在 Handle 中实际使用 group。
	// 如果需要在 Handle 中使用组名（例如，作为日志字段），逻辑会更复杂。
}

func TestSlog(t *testing.T) {
	// 打开 SQLite3 数据库
	db, err := sql.Open("sqlite3", "logs.db")
	if err != nil {
		panic(err)
	}
	defer db.Close()

	// 创建日志表
	_, err = db.Exec(`CREATE TABLE IF NOT EXISTS logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        level TEXT,
        message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )`)
	if err != nil {
		panic(err)
	}

	// 创建自定义日志处理器
	handler := NewSQLiteHandler(db)

	// 创建日志记录器
	logger := slog.New(handler)

	// 记录日志
	logger.Info("Starting application", "version", "1.0.0")
	logger.Error("An error occurred", "error", "some error message")
}

func TestSlogs(t *testing.T) {
	// 打开 SQLite3 数据库
	db, err := sql.Open("sqlite3", "logs.db")
	if err != nil {
		panic(err)
	}
	defer db.Close()
}

func TestXxx(t *testing.T) {

}
