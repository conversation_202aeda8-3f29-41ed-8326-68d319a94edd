package test

import (
	"context"
	"fmt"
	"html/template"
	"log"
	"os"
	"path/filepath"
	"time"

	"github.com/chromedp/chromedp"
)

// 定义坐标点结构（可选，也可以直接用 [][]float64）
type Point struct {
	Lat float64
	Lon float64
}

// 用于传递给 HTML 模板的数据
type MapData struct {
	CenterLat float64
	CenterLon float64
	Zoom      int
	Points    [][]float64 // Leaflet.heat 通常需要 [lat, lon] 或 [lat, lon, intensity] 格式
	TileURL   string
	TileAttr  string
}

// HTML 模板内容
const heatmapHTMLTemplate = `
<!DOCTYPE html>
<html>
<head>
	<title>Heatmap</title>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
	<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
	<script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
	<style>
		html, body, #map { height: 100%; width: 100%; margin: 0; padding: 0; }
	</style>
</head>
<body>
<div id="map"></div>
<script>
	var map = L.map('map').setView([{{.CenterLat}}, {{.CenterLon}}], {{.Zoom}});

	L.tileLayer('{{.TileURL}}', {
		attribution: '{{.TileAttr}}'
	}).addTo(map);

	var heatPoints = {{.Points}}; // 接收 Go 传递过来的数据

	L.heatLayer(heatPoints, {
		radius: 25, // 热力图点的半径，可以调整
		blur: 15,   // 模糊度，可以调整
		maxZoom: 18 // 在哪个缩放级别之前显示热力图
	}).addTo(map);

	// 添加一个标记，表示地图已准备好（用于截图等待）
	document.body.setAttribute('data-map-ready', 'true');

</script>
</body>
</html>
`

func main1() {
	// 1. 定义数据 (与 Python 脚本一致)
	latData := []float64{
		39.904031, 39.912345, 39.908002, 39.904039,
		39.904032, 39.912345, 39.908001, 39.904031,
		39.904033, 39.912345, 39.908004, 39.904030,
		39.904034, 39.912345, 39.908005, 39.904032,
		39.904035, 39.912346, 39.908006, 39.904033,
		39.904036, 39.912346, 39.908008, 39.904035,
	}
	lonData := []float64{
		116.407521, 116.412225, 116.405020, 116.407526,
		116.407522, 116.412222, 116.405040, 116.407525,
		116.407523, 116.412242, 116.405050, 116.407529,
		116.407524, 116.412262, 116.405010, 116.407526,
		116.407525, 116.412225, 116.405060, 116.407521,
		116.407526, 116.412222, 116.405040, 116.407526,
	}

	// 将数据转换为 Leaflet.heat 需要的格式 [[lat, lon], ...]
	pointsForMap := make([][]float64, len(latData))
	for i := range latData {
		pointsForMap[i] = []float64{latData[i], lonData[i]}
	}

	// 准备模板数据
	mapRenderData := MapData{
		CenterLat: latData[0],
		CenterLon: lonData[0],
		Zoom:      12,
		Points:    pointsForMap,
		TileURL:   "https://mt1.google.com/vt/lyrs=m&x={x}&y={y}&z={z}", // 与 Python 脚本一致
		TileAttr:  "Google Maps",
	}

	// 2. 生成 HTML 文件
	htmlFilePath := "heatmap.html"
	err := generateHTML(htmlFilePath, mapRenderData)
	if err != nil {
		log.Fatalf("无法生成 HTML 文件: %v", err)
	}
	log.Printf("HTML 文件已生成: %s", htmlFilePath)

	// 3. 使用 chromedp 截图
	pngFilePath := "heatmap.png"
	err = captureScreenshot(htmlFilePath, pngFilePath)
	if err != nil {
		log.Fatalf("无法截图: %v", err)
	}
	log.Printf("热力图已保存为 PNG 图片: %s", pngFilePath)
}

// generateHTML 使用模板生成 HTML 文件
func generateHTML(filePath string, data MapData) error {
	tmpl, err := template.New("heatmap").Parse(heatmapHTMLTemplate)
	if err != nil {
		return fmt.Errorf("解析模板失败: %w", err)
	}

	file, err := os.Create(filePath)
	if err != nil {
		return fmt.Errorf("创建文件失败: %w", err)
	}
	defer file.Close()

	// 直接将 [][]float64 传递给模板，它会被 Go 的模板引擎自动处理成类似 JSON 的格式
	err = tmpl.Execute(file, data)
	if err != nil {
		return fmt.Errorf("执行模板失败: %w", err)
	}

	return nil
}

// captureScreenshot 使用 chromedp 打开 HTML 文件并截图
func captureScreenshot(htmlFilePath, pngFilePath string) error {
	// 获取 HTML 文件的绝对路径
	absHtmlPath, err := filepath.Abs(htmlFilePath)
	if err != nil {
		return fmt.Errorf("获取绝对路径失败: %w", err)
	}
	fileURL := "file://" + absHtmlPath

	// 创建 chromedp 上下文
	// 增加 alloc Opts 来设置窗口大小，模拟 Python 脚本中的 viewport
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.WindowSize(1920, 1080), // 设置视口大小
	)
	allocCtx, cancelAlloc := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancelAlloc()

	ctx, cancel := chromedp.NewContext(allocCtx, chromedp.WithLogf(log.Printf))
	defer cancel()

	// 创建一个超时上下文，防止截图卡死
	ctx, cancel = context.WithTimeout(ctx, 60*time.Second) // 60秒超时
	defer cancel()

	// 定义截图缓冲区
	var buf []byte

	log.Printf("正在打开: %s", fileURL)
	// 执行截图任务
	err = chromedp.Run(ctx,
		chromedp.Navigate(fileURL),
		// 等待地图渲染完成的标记出现
		chromedp.WaitVisible(`body[data-map-ready="true"]`, chromedp.ByQuery),
		// 增加短暂延时，确保热力图渲染（类似 Python 中的 wait_for_timeout）
		chromedp.Sleep(3*time.Second), // 增加到 3 秒试试
		// 截取整个页面
		chromedp.FullScreenshot(&buf, 90), // 90 是图片质量 (0-100)
	)
	if err != nil {
		return fmt.Errorf("chromedp 运行失败: %w", err)
	}

	// 将截图数据写入文件
	err = os.WriteFile(pngFilePath, buf, 0644)
	if err != nil {
		return fmt.Errorf("写入 PNG 文件失败: %w", err)
	}

	return nil
}
