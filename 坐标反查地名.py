import requests
import json
import time
from typing import Dict, List, Optional

class LocationReverseGeocoder:
    """坐标反查地名工具类"""

    def __init__(self):
        self.google_api_key = "AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })

    def google_reverse_geocode(self, lat: float, lng: float, language: str = 'zh-CN') -> Dict:
        """
        使用Google Geocoding API进行反向地理编码

        :param lat: 纬度
        :param lng: 经度
        :param language: 语言设置，默认为中文
        :return: 地理信息字典
        """
        url = "https://maps.googleapis.com/maps/api/geocode/json"
        params = {
            'latlng': f"{lat},{lng}",
            'key': self.google_api_key,
            'language': language,
            'region': 'CN' if language == 'zh-CN' else 'US'
        }

        try:
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if data['status'] == 'OK' and data['results']:
                result = data['results'][0]

                # 解析地址组件
                components = {}
                for component in result.get('address_components', []):
                    for type_name in component['types']:
                        components[type_name] = component['long_name']

                return {
                    'status': 'success',
                    'formatted_address': result.get('formatted_address', ''),
                    'components': components,
                    'place_id': result.get('place_id', ''),
                    'location_type': result.get('geometry', {}).get('location_type', ''),
                    'raw_data': data
                }
            else:
                return {
                    'status': 'error',
                    'message': f"Google API错误: {data.get('status', 'Unknown error')}",
                    'raw_data': data
                }

        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'message': f"请求失败: {str(e)}"
            }

    def nominatim_reverse_geocode(self, lat: float, lng: float, language: str = 'zh') -> Dict:
        """
        使用OpenStreetMap Nominatim API进行反向地理编码（免费）

        :param lat: 纬度
        :param lng: 经度
        :param language: 语言设置
        :return: 地理信息字典
        """
        url = "https://nominatim.openstreetmap.org/reverse"
        params = {
            'lat': lat,
            'lon': lng,
            'format': 'json',
            'addressdetails': 1,
            'accept-language': language,
            'zoom': 18
        }

        try:
            # Nominatim要求限制请求频率
            time.sleep(1)

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if 'display_name' in data:
                return {
                    'status': 'success',
                    'formatted_address': data.get('display_name', ''),
                    'components': data.get('address', {}),
                    'place_id': data.get('place_id', ''),
                    'osm_type': data.get('osm_type', ''),
                    'osm_id': data.get('osm_id', ''),
                    'raw_data': data
                }
            else:
                return {
                    'status': 'error',
                    'message': "未找到地址信息",
                    'raw_data': data
                }

        except requests.exceptions.RequestException as e:
            return {
                'status': 'error',
                'message': f"请求失败: {str(e)}"
            }

    def reverse_geocode_multiple_sources(self, lat: float, lng: float) -> Dict:
        """
        使用多个数据源进行反向地理编码

        :param lat: 纬度
        :param lng: 经度
        :return: 综合结果
        """
        results = {}

        print(f"正在查询坐标: {lat}, {lng}")
        print("-" * 50)

        # Google Geocoding API
        print("1. 使用Google Geocoding API查询...")
        google_result = self.google_reverse_geocode(lat, lng)
        results['google'] = google_result

        if google_result['status'] == 'success':
            print(f"   ✓ Google地址: {google_result['formatted_address']}")
        else:
            print(f"   ✗ Google查询失败: {google_result['message']}")

        # OpenStreetMap Nominatim
        print("\n2. 使用OpenStreetMap Nominatim查询...")
        osm_result = self.nominatim_reverse_geocode(lat, lng)
        results['openstreetmap'] = osm_result

        if osm_result['status'] == 'success':
            print(f"   ✓ OSM地址: {osm_result['formatted_address']}")
        else:
            print(f"   ✗ OSM查询失败: {osm_result['message']}")

        return results

    def format_address_components(self, components: Dict) -> str:
        """
        格式化地址组件为可读格式

        :param components: 地址组件字典
        :return: 格式化的地址字符串
        """
        # Google API组件映射
        google_mapping = {
            'country': '国家',
            'administrative_area_level_1': '省/州',
            'administrative_area_level_2': '市',
            'administrative_area_level_3': '区/县',
            'locality': '城市',
            'sublocality': '街道',
            'route': '路',
            'street_number': '门牌号',
            'postal_code': '邮编'
        }

        # OSM组件映射
        osm_mapping = {
            'country': '国家',
            'state': '省/州',
            'city': '市',
            'county': '区/县',
            'town': '镇',
            'village': '村',
            'suburb': '街道',
            'road': '路',
            'house_number': '门牌号',
            'postcode': '邮编'
        }

        formatted_parts = []

        # 尝试Google格式
        for key, value in components.items():
            if key in google_mapping and value:
                formatted_parts.append(f"{google_mapping[key]}: {value}")

        # 如果没有Google格式，尝试OSM格式
        if not formatted_parts:
            for key, value in components.items():
                if key in osm_mapping and value:
                    formatted_parts.append(f"{osm_mapping[key]}: {value}")

        return "\n".join(formatted_parts) if formatted_parts else "无详细组件信息"

def main():
    """主函数"""
    geocoder = LocationReverseGeocoder()

    # 测试坐标
    test_lat = 25.102341196351926
    test_lng = 121.54851212861129

    print("=" * 60)
    print("坐标反查地名工具")
    print("=" * 60)

    # 执行反向地理编码
    results = geocoder.reverse_geocode_multiple_sources(test_lat, test_lng)

    print("\n" + "=" * 60)
    print("详细结果")
    print("=" * 60)

    # 显示Google结果
    if results['google']['status'] == 'success':
        google_data = results['google']
        print("\n🌍 Google Maps 结果:")
        print(f"完整地址: {google_data['formatted_address']}")
        print(f"位置类型: {google_data['location_type']}")
        print(f"Place ID: {google_data['place_id']}")
        print("\n地址组件:")
        print(geocoder.format_address_components(google_data['components']))

    # 显示OSM结果
    if results['openstreetmap']['status'] == 'success':
        osm_data = results['openstreetmap']
        print(f"\n🗺️ OpenStreetMap 结果:")
        print(f"完整地址: {osm_data['formatted_address']}")
        print(f"OSM类型: {osm_data['osm_type']}")
        print(f"OSM ID: {osm_data['osm_id']}")
        print("\n地址组件:")
        print(geocoder.format_address_components(osm_data['components']))

    # 保存结果到JSON文件
    output_file = f"reverse_geocode_result_{int(time.time())}.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)

    print(f"\n💾 详细结果已保存到: {output_file}")

    return results

if __name__ == "__main__":
    main()