<!DOCTYPE html>
<html>
<head>
    
    <meta http-equiv="content-type" content="text/html; charset=UTF-8" />
    <script src="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/leaflet@1.9.3/dist/leaflet.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.2/dist/css/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap-glyphicons.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.0/css/all.min.css"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/Leaflet.awesome-markers/2.0.2/leaflet.awesome-markers.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/python-visualization/folium/folium/templates/leaflet.awesome.rotate.min.css"/>
    
            <meta name="viewport" content="width=device-width,
                initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
            <style>
                #map_0c87868fa14e853f2282c3180e3725fa {
                    position: relative;
                    width: 100.0%;
                    height: 100.0%;
                    left: 0.0%;
                    top: 0.0%;
                }
                .leaflet-container { font-size: 1rem; }
            </style>

            <style>html, body {
                width: 100%;
                height: 100%;
                margin: 0;
                padding: 0;
            }
            </style>

            <style>#map {
                position:absolute;
                top:0;
                bottom:0;
                right:0;
                left:0;
                }
            </style>

            <script>
                L_NO_TOUCH = false;
                L_DISABLE_3D = false;
            </script>

        
    <script src="https://cdn.jsdelivr.net/gh/python-visualization/folium@main/folium/templates/leaflet_heat.min.js"></script>
</head>
<body>
    
    
            <div class="folium-map" id="map_0c87868fa14e853f2282c3180e3725fa" ></div>
        
</body>
<script>
    
    
            var map_0c87868fa14e853f2282c3180e3725fa = L.map(
                "map_0c87868fa14e853f2282c3180e3725fa",
                {
                    center: [39.90403, 116.407526],
                    crs: L.CRS.EPSG3857,
                    ...{
  "zoom": 12,
  "zoomControl": true,
  "preferCanvas": false,
}

                }
            );

            

        
    
            var tile_layer_348cfb93d606fea95aaab88f64983269 = L.tileLayer(
                "https://mt1.google.com/vt/lyrs=m\u0026x={x}\u0026y={y}\u0026z={z}\u0026hl=zh-CN",
                {
  "minZoom": 0,
  "maxZoom": 18,
  "maxNativeZoom": 18,
  "noWrap": false,
  "attribution": "Google Maps",
  "subdomains": "abc",
  "detectRetina": false,
  "tms": false,
  "opacity": 1,
}

            );
        
    
            tile_layer_348cfb93d606fea95aaab88f64983269.addTo(map_0c87868fa14e853f2282c3180e3725fa);
        
    
            var heat_map_ed157c104914e080be229bc7735ed883 = L.heatLayer(
                [[39.90403, 116.407526], [39.912345, 116.412222], [39.908, 116.405], [39.90403, 116.407526], [39.90403, 116.407526], [39.912345, 116.412222], [39.908, 116.405], [39.90403, 116.407526], [39.90403, 116.407526], [39.912345, 116.412222], [39.908, 116.405], [39.90403, 116.407526], [39.90403, 116.407526], [39.912345, 116.412222], [39.908, 116.405], [39.90403, 116.407526], [39.90403, 116.407526], [39.912345, 116.412222], [39.908, 116.405], [39.90403, 116.407526], [39.90403, 116.407526], [39.912345, 116.412222], [39.908, 116.405], [39.90403, 116.407526]],
                {
  "minOpacity": 0.5,
  "maxZoom": 18,
  "radius": 25,
  "blur": 15,
}
            );
        
    
            heat_map_ed157c104914e080be229bc7735ed883.addTo(map_0c87868fa14e853f2282c3180e3725fa);
        
</script>
</html>