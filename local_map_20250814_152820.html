
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>轨迹地图</title>
        <style>
            #map {
                height: 100%;
                width: 100%;
                position: absolute;
                top: 0;
                left: 0;
            }
            html, body {
                height: 100%;
                margin: 0;
                padding: 0;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            // 位置数据
            const locations = [{"name": "\u53f0\u5317101", "lat": 25.0338, "lng": 121.5646, "timestamp": 1624240800}, {"name": "\u53f0\u5317\u8f66\u7ad9", "lat": 25.0478, "lng": 121.517, "timestamp": 1624248000}, {"name": "\u56fd\u7acb\u53f0\u6e7e\u5927\u5b66", "lat": 25.0174, "lng": 121.5399, "timestamp": 1624255200}];
            
            function initMap() {
                // 创建地图
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: 25.0338, lng: 121.5646 }
                });
                
                const bounds = new google.maps.LatLngBounds();
                const markers = [];
                const infoWindows = [];
                
                // 为每个位置创建标记
                locations.forEach((location, index) => {
                    const position = new google.maps.LatLng(location.lat, location.lng);
                    bounds.extend(position);
                    
                    // 创建标记
                    const marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: location.name,
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            fillColor: '#0066FF',
                            fillOpacity: 1,
                            strokeColor: '#FFFFFF',
                            strokeWeight: 2,
                            scale: 10
                        },
                        zIndex: 1000 - index
                    });
                    
                    markers.push(marker);
                    
                    // 创建标签
                    let labelContent = location.name;
                    if (location.timestamp) {
                        const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                        labelContent += `<br><span style="font-size:12px;">${timeStr}</span>`;
                    }
                    
                    const label = new google.maps.InfoWindow({
                        content: `<div style="color:red; font-weight:bold; font-size:16px;">${labelContent}</div>`,
                        position: position,
                        disableAutoPan: true
                    });
                    
                    label.open(map);
                    
                    // 创建信息窗口
                    if (location.details || location.timestamp) {
                        let infoContent = `
                            <div style="min-width:200px;">
                                <h3>${location.name}</h3>
                                <p>纬度: ${location.lat}</p>
                                <p>经度: ${location.lng}</p>
                        `;
                        
                        if (location.timestamp) {
                            const timeStr = new Date(location.timestamp * 1000).toLocaleString();
                            infoContent += `<p>时间: ${timeStr}</p>`;
                        }
                        
                        if (location.details) {
                            infoContent += `<p>${location.details}</p>`;
                        }
                        
                        infoContent += '</div>';
                        
                        const infoWindow = new google.maps.InfoWindow({
                            content: infoContent
                        });
                        
                        infoWindows.push(infoWindow);
                        
                        marker.addListener('click', () => {
                            infoWindows.forEach(window => window.close());
                            infoWindow.open(map, marker);
                        });
                    }
                });
                
                // 连接各点的直线
                let pathCoordinates = locations.map(loc => ({
                    lat: loc.lat, 
                    lng: loc.lng
                }));
                
                const polyline = new google.maps.Polyline({
                    path: pathCoordinates,
                    geodesic: true,
                    strokeColor: '#0066FF',
                    strokeOpacity: 1.0,
                    strokeWeight: 3,
                    map: map
                });
                
                // 调整视图以显示所有标记
                map.fitBounds(bounds);
                
                // 如果只有一个位置，设置适当的缩放级别
                if (locations.length === 1) {
                    map.setZoom(15);
                }
            }
        </script>
        <script src="https://maps.googleapis.com/maps/api/js?callback=initMap" async defer></script>
    </body>
    </html>
    