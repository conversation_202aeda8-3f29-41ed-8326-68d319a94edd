package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"log/slog"
	"os"
	"sync"
	"time"

	_ "github.com/mattn/go-sqlite3"
)

// ------------------------------------------
// 自定义 SQLite Handler
// ------------------------------------------
type SQLiteHandler struct {
	db   *sql.DB // 数据库连接
	opts slog.HandlerOptions
	mu   sync.Mutex // 保证并发安全
}

// 实现 slog.Handler 接口
func (h *SQLiteHandler) Enabled(_ context.Context, level slog.Level) bool {
	return level >= h.opts.Level.Level()
}

func (h *SQLiteHandler) Handle(ctx context.Context, r slog.Record) error {
	// 将日志属性转换为 JSON
	attrs := make(map[string]any)
	r.Attrs(func(a slog.Attr) bool {
		attrs[a.Key] = a.Value.Any()
		return true
	})
	attrsJSON, _ := json.Marshal(attrs)

	// 插入数据库（加锁保证线程安全）
	h.mu.Lock()
	defer h.mu.Unlock()

	_, err := h.db.Exec(
		`INSERT INTO logss (time, level, message, attributes) 
         VALUES (?, ?, ?, ?)`,
		r.Time.UTC().Format(time.RFC3339), // 格式化时间
		r.Level.String(),
		r.Message,
		string(attrsJSON),
	)
	return err
}

func (h *SQLiteHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	// 简化实现：直接返回当前 Handler（可根据需要扩展）
	return h
}

func (h *SQLiteHandler) WithGroup(name string) slog.Handler {
	// 简化实现：直接返回当前 Handler
	return h
}

// 构造函数
func NewSQLiteHandler(db *sql.DB, opts *slog.HandlerOptions) *SQLiteHandler {
	if opts == nil {
		opts = &slog.HandlerOptions{}
	}
	return &SQLiteHandler{db: db, opts: *opts}
}

// ------------------------------------------
// 主程序
// ------------------------------------------
func mainLog() {
	// 1. 创建或打开 SQLite 数据库
	db, err := sql.Open("sqlite3", "logs.db")
	if err != nil {
		slog.Error("无法打开数据库", "error", err)
		os.Exit(1)
	}
	defer db.Close()

	// 2. 创建日志表（如果不存在）
	_, err = db.Exec(`
        CREATE TABLE IF NOT EXISTS logss (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            time TEXT NOT NULL,
            level TEXT NOT NULL,
            message TEXT NOT NULL,
            attributes TEXT
        )
    `)
	if err != nil {
		slog.Error("创建表失败", "error", err)
		os.Exit(1)
	}

	// 3. 创建自定义 Handler
	handler := NewSQLiteHandler(db, &slog.HandlerOptions{
		Level: slog.LevelDebug, // 记录 Debug 及以上级别
	})

	// 4. 创建 Logger
	logger := slog.New(handler)

	// 5. 记录日志
	logger.Debug("调试信息", "request_id", "abc123", "path", "/api/test")
	logger.Info("用户登录", "user_id", 42, "ip", "*************")
	logger.Warn("高延迟警告", "latency_ms", 1500, "threshold", 1000)
	logger.Error("文件未找到", "error", "file.txt not found", "code", 404)
}
