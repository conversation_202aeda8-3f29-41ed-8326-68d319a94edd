from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from pptx.enum.text import PP_ALIGN
from pptx.enum.shapes import MSO_VERTICAL_ANCHOR

# --- 设置 ---
# 设置颜色
blue_background = RGBColor(230, 245, 255)
dark_text = RGBColor(0, 0, 0)
title_color = RGBColor(0, 117, 255)
# 幻灯片布局：1为"标题和内容"，5为"仅标题"
TITLE_AND_CONTENT_LAYOUT = 1
TITLE_ONLY_LAYOUT = 5

# --- 创建PPT对象 ---
prs = Presentation()
# 使用更现代的16:9宽屏布局
prs.slide_width = Inches(16)
prs.slide_height = Inches(9)


# --- 辅助函数：设置单元格样式 ---
def set_cell_style(cell, size=Pt(16)):
    """设置表格单元格的文本样式和垂直居中"""
    cell.vertical_anchor = MSO_VERTICAL_ANCHOR.MIDDLE
    p = cell.text_frame.paragraphs[0]
    p.font.size = size
    p.alignment = PP_ALIGN.CENTER


# === Slide 1: 封面页 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_ONLY_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "XXX项目 项目结项汇报"
title.text_frame.paragraphs[0].font.size = Pt(44)
title.text_frame.paragraphs[0].font.bold = True
title.text_frame.paragraphs[0].font.color.rgb = title_color

# 使用文本框精确定位
txBox = slide.shapes.add_textbox(Inches(2), Inches(3.0), Inches(12), Inches(4))
tf = txBox.text_frame
tf.clear()

p1 = tf.add_paragraph()
p1.text = "成果回顾 · 经验总结 · 后续规划"
p1.alignment = PP_ALIGN.CENTER
p1.font.size = Pt(28)
p1.font.color.rgb = dark_text

for text in ["", "项目周期：2025年4月15日 - 2025年6月15日", "项目经理：张三", "汇报时间：2025年6月20日"]:
    p = tf.add_paragraph()
    p.text = text
    p.alignment = PP_ALIGN.CENTER
    p.font.size = Pt(20)
    p.font.color.rgb = dark_text


# === Slide 2: 目录页 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_AND_CONTENT_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "汇报内容"
title.text_frame.paragraphs[0].font.size = Pt(36)
title.text_frame.paragraphs[0].font.color.rgb = title_color

content = slide.shapes.placeholders[1].text_frame
content.clear()
for item in ["项目目标与完成情况", "核心成果展示", "经验教训与改进建议", "项目总结与后续计划"]:
    p = content.add_paragraph()
    p.text = item
    p.font.size = Pt(28)
    p.font.color.rgb = dark_text
    p.level = 0
    p.line_spacing = 1.5


# === Slide 3: 目标与完成情况 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_AND_CONTENT_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "目标回顾与完成情况"
title.text_frame.paragraphs[0].font.size = Pt(36)
title.text_frame.paragraphs[0].font.color.rgb = title_color

content_shape = slide.shapes.placeholders[1]
content_shape.text_frame.clear()  # 清除占位符中的默认文本

# *** FIX: 表格列数应为3 ***
tb = slide.shapes.add_table(rows=4, cols=3, left=Inches(1.5), top=Inches(2), width=Inches(13), height=Inches(3)).table

# 表头
tb.cell(0, 0).text = "序号"
tb.cell(0, 1).text = "目标描述"
tb.cell(0, 2).text = "完成情况"

# 填写目标
targets = [("1", "开发客户端并支持用户登录", "✅ 已完成"), ("2", "集成UKey认证工具", "✅ 已完成"), ("3", "系统接口联调并上线", "✅ 已完成")]
for i, (idx, desc, stat) in enumerate(targets, start=1):
    tb.cell(i, 0).text = idx
    tb.cell(i, 1).text = desc
    tb.cell(i, 2).text = stat

# 统一设置表格样式
for cell in tb.iter_cells():
    set_cell_style(cell)

# 插入备注
txBox = slide.shapes.add_textbox(Inches(1.5), Inches(5.5), Inches(13), Inches(1))
p = txBox.text_frame.add_paragraph()
p.text = "项目完成率：100%"
p.font.size = Pt(24)
p.font.bold = True
p.font.color.rgb = RGBColor(22, 160, 133) # 使用更醒目的颜色


# === Slide 4 to 6: 核心成果展示 (通用模板) ===
achievements = [
    {
        "title": "核心成果一：客户端界面展示",
        "placeholders": ["（此处插入客户端登录界面截图）", "（此处插入主功能页面截图）"],
        "desc": "功能说明：\n• 支持用户登录、身份验证\n• 集成UKey认证入口\n• 界面简洁，兼容多分辨率"
    },
    {
        "title": "核心成果二：UKey认证工具集成",
        "placeholders": ["（此处插入UKey驱动安装流程图）", "（此处插入UKey登录界面截图）"],
        "desc": "功能说明：\n• 实现UKey硬件认证机制\n• 与后台安全系统无缝对接\n• 用户需插入UKey并验证PIN码完成登录"
    },
    {
        "title": "核心成果三：系统对接与功能验证",
        "placeholders": ["（此处插入接口调用流程图）", "（此处插入灰度部署上线界面截图）"],
        "desc": "说明：\n• 所有接口完成开发与联调测试\n• 功能与性能测试通过\n• 已完成灰度发布，运行稳定"
    }
]

for ach in achievements:
    slide = prs.slides.add_slide(prs.slide_layouts[TITLE_ONLY_LAYOUT])
    slide.background.fill.solid()
    slide.background.fill.fore_color.rgb = blue_background

    title = slide.shapes.title
    title.text = ach["title"]
    title.text_frame.paragraphs[0].font.size = Pt(36)
    title.text_frame.paragraphs[0].font.color.rgb = title_color

    # 左侧占位符
    txBox_left = slide.shapes.add_textbox(Inches(1), Inches(2), Inches(6.5), Inches(4))
    p_left = txBox_left.text_frame.add_paragraph()
    p_left.text = ach["placeholders"][0]
    p_left.font.size = Pt(18); p_left.font.italic = True; p_left.alignment = PP_ALIGN.CENTER

    # 右侧占位符
    txBox_right = slide.shapes.add_textbox(Inches(8.5), Inches(2), Inches(6.5), Inches(4))
    p_right = txBox_right.text_frame.add_paragraph()
    p_right.text = ach["placeholders"][1]
    p_right.font.size = Pt(18); p_right.font.italic = True; p_right.alignment = PP_ALIGN.CENTER

    # 底部描述
    txBox_desc = slide.shapes.add_textbox(Inches(1), Inches(6.5), Inches(14), Inches(2))
    p_desc = txBox_desc.text_frame.add_paragraph()
    p_desc.text = ach["desc"]
    p_desc.font.size = Pt(20); p_desc.font.color.rgb = dark_text; p_desc.line_spacing = 1.5


# === Slide 7: 问题与经验总结 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_AND_CONTENT_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "问题回顾与经验总结"
title.text_frame.paragraphs[0].font.size = Pt(36)
title.text_frame.paragraphs[0].font.color.rgb = title_color

slide.shapes.placeholders[1].text_frame.clear()

tb = slide.shapes.add_table(rows=3, cols=3, left=Inches(1.5), top=Inches(2), width=Inches(13), height=Inches(2)).table
tb.cell(0, 0).text = "问题"; tb.cell(0, 1).text = "影响"; tb.cell(0, 2).text = "教训总结"

problems = [("UKey兼容性测试不足", "初期用户设备无法识别", "应提前准备多种UKey硬件测试环境"), ("客户端依赖较多", "安装包大、部署复杂", "需优化依赖管理，提升部署效率")]
for i, (prob, impact, lesson) in enumerate(problems, start=1):
    tb.cell(i, 0).text = prob; tb.cell(i, 1).text = impact; tb.cell(i, 2).text = lesson

for cell in tb.iter_cells():
    set_cell_style(cell, size=Pt(14))

txBox = slide.shapes.add_textbox(Inches(1.5), Inches(4.5), Inches(13), Inches(3))
p = txBox.text_frame.add_paragraph()
p.text = "经验总结：\n• 需加强前期技术调研与风险评估\n• 模块化开发能提升开发效率与后期维护性\n• 测试阶段应模拟真实用户场景"
p.font.size = Pt(20); p.font.color.rgb = dark_text; p.line_spacing = 1.5


# === Slide 8: 改进建议 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_AND_CONTENT_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "改进建议"
title.text_frame.paragraphs[0].font.size = Pt(36)
title.text_frame.paragraphs[0].font.color.rgb = title_color

content = slide.shapes.placeholders[1].text_frame
content.clear()
suggestions = [
    "建议一：建立UKey硬件测试标准流程\n   （建立统一测试环境与常用UKey清单）",
    "建议二：客户端模块化部署\n   （按需安装核心功能与扩展模块，减小安装包体积）",
    "建议三：加强用户沟通机制\n   （建立用户反馈渠道，提前收集使用意见和建议）"
]
for item in suggestions:
    p = content.add_paragraph()
    p.text = "✅ " + item
    p.font.size = Pt(22); p.font.color.rgb = dark_text; p.line_spacing = 1.5


# === Slide 9: 项目总结与后续安排 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_ONLY_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "项目总结与后续安排"
title.text_frame.paragraphs[0].font.size = Pt(36)
title.text_frame.paragraphs[0].font.color.rgb = title_color

txBox_summary = slide.shapes.add_textbox(Inches(1.5), Inches(1.8), Inches(13), Inches(2))
p = txBox_summary.text_frame.add_paragraph()
p.text = "✅ 项目总结：项目目标全部达成\n    • 客户端上线运行稳定\n    • UKey认证机制完整集成\n    • 系统接口对接完成"
p.font.size = Pt(20); p.font.color.rgb = dark_text; p.line_spacing = 1.5

txBox_plan_title = slide.shapes.add_textbox(Inches(1.5), Inches(3.8), Inches(13), Inches(1))
p_plan = txBox_plan_title.text_frame.add_paragraph()
p_plan.text = "🚀 后续计划："
p_plan.font.size = Pt(20); p_plan.font.color.rgb = dark_text; p_plan.font.bold = True

# *** FIX: 表格列数应为3 ***
tb = slide.shapes.add_table(rows=5, cols=3, left=Inches(1.5), top=Inches(4.5), width=Inches(13), height=Inches(3)).table
tb.cell(0, 0).text = "时间段"; tb.cell(0, 1).text = "任务"; tb.cell(0, 2).text = "说明"

plans = [
    ("即将启动", "全量上线", "在灰度发布基础上，逐步向全体用户开放"),
    ("持续进行", "用户培训", "完善用户手册与培训视频"),
    ("Q3-Q4计划", "客户端功能迭代", "考虑移动端支持、功能扩展"),
    ("后续阶段", "系统集成扩展", "推动与其他系统的对接"),
]
for i, (when, task, desc) in enumerate(plans, start=1):
    tb.cell(i, 0).text = when; tb.cell(i, 1).text = task; tb.cell(i, 2).text = desc

for cell in tb.iter_cells():
    set_cell_style(cell, size=Pt(14))


# === Slide 10: 结束页 ===
slide = prs.slides.add_slide(prs.slide_layouts[TITLE_ONLY_LAYOUT])
slide.background.fill.solid()
slide.background.fill.fore_color.rgb = blue_background

title = slide.shapes.title
title.text = "感谢聆听"
title.text_frame.paragraphs[0].font.size = Pt(60)
title.text_frame.paragraphs[0].font.bold = True
title.text_frame.paragraphs[0].font.color.rgb = title_color
title.top = Inches(3.0); title.left=0; title.width=prs.slide_width
title.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER

txBox = slide.shapes.add_textbox(Inches(2), Inches(5.0), Inches(12), Inches(2))
tf = txBox.text_frame
for text in ["团队成员：张三、李四、王五、赵六", "项目经理邮箱：<EMAIL>"]:
    p = tf.add_paragraph()
    p.text = text
    p.font.size = Pt(20); p.font.color.rgb = dark_text; p.alignment = PP_ALIGN.CENTER


# --- 保存文件 ---
file_path = '项目结项汇报.pptx'
prs.save(file_path)
print(f"✅ PPT 文件已成功生成：{file_path}")