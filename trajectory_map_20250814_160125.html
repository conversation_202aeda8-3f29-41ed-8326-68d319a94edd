
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>轨迹地图</title>
        <style>
            #map {
                height: 100vh;
                width: 100vw;
            }
            body {
                margin: 0;
                padding: 0;
                font-family: Arial, sans-serif;
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        <script>
            const locations = [{"name": "\u5929\u5b89\u95e8\u5e7f\u573a", "lat": 39.9042, "lng": 116.4074, "timestamp": 1624240800, "details": "\u4e2d\u534e\u4eba\u6c11\u5171\u548c\u56fd\u7684\u8c61\u5f81"}, {"name": "\u6545\u5bab\u535a\u7269\u9662", "lat": 39.9163, "lng": 116.3972, "timestamp": 1624248000, "details": "\u660e\u6e05\u4e24\u671d\u7684\u7687\u5bb6\u5bab\u6bbf"}, {"name": "\u5929\u575b\u516c\u56ed", "lat": 39.8822, "lng": 116.4066, "timestamp": 1624255200, "details": "\u660e\u6e05\u7687\u5e1d\u796d\u5929\u7684\u573a\u6240"}, {"name": "\u9890\u548c\u56ed", "lat": 39.9999, "lng": 116.2755, "timestamp": 1624262400, "details": "\u4e2d\u56fd\u53e4\u5178\u56ed\u6797\u7684\u6770\u4f5c"}];
            
            function initMap() {
                const map = new google.maps.Map(document.getElementById('map'), {
                    zoom: 12,
                    center: { lat: 39.9042, lng: 116.4074 },
                    mapTypeId: 'roadmap'
                });
                
                const bounds = new google.maps.LatLngBounds();
                const markers = [];
                
                // 创建标记
                locations.forEach((location, index) => {
                    const position = new google.maps.LatLng(location.lat, location.lng);
                    bounds.extend(position);
                    
                    const marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        title: location.name,
                        label: {
                            text: (index + 1).toString(),
                            color: 'white',
                            fontWeight: 'bold'
                        },
                        icon: {
                            path: google.maps.SymbolPath.CIRCLE,
                            fillColor: index === 0 ? '#00FF00' : (index === locations.length - 1 ? '#FF0000' : '#0066FF'),
                            fillOpacity: 1,
                            strokeColor: '#FFFFFF',
                            strokeWeight: 2,
                            scale: 12
                        }
                    });
                    
                    markers.push(marker);
                    
                    // 信息窗口
                    let infoContent = `
                        <div style="min-width:200px;">
                            <h3>${location.name}</h3>
                            <p><strong>位置:</strong> ${location.lat.toFixed(6)}, ${location.lng.toFixed(6)}</p>
                    `;
                    
                    if (location.timestamp) {
                        const timeStr = new Date(location.timestamp * 1000).toLocaleString('zh-CN');
                        infoContent += `<p><strong>时间:</strong> ${timeStr}</p>`;
                    }
                    
                    if (location.details) {
                        infoContent += `<p><strong>详情:</strong> ${location.details}</p>`;
                    }
                    
                    infoContent += '</div>';
                    
                    const infoWindow = new google.maps.InfoWindow({
                        content: infoContent
                    });
                    
                    marker.addListener('click', () => {
                        markers.forEach(m => {
                            if (m.infoWindow) m.infoWindow.close();
                        });
                        infoWindow.open(map, marker);
                        marker.infoWindow = infoWindow;
                    });
                });
                
                // 创建轨迹线
                const pathCoordinates = locations.map(loc => ({
                    lat: loc.lat, 
                    lng: loc.lng
                }));
                
                const polyline = new google.maps.Polyline({
                    path: pathCoordinates,
                    geodesic: true,
                    strokeColor: '#0066FF',
                    strokeOpacity: 1.0,
                    strokeWeight: 4,
                    map: map
                });
                
                // 调整视图
                if (locations.length > 1) {
                    map.fitBounds(bounds);
                    const padding = { top: 50, right: 50, bottom: 50, left: 50 };
                    map.fitBounds(bounds, padding);
                } else {
                    map.setZoom(15);
                }
                
                // 标记地图加载完成
                window.mapLoaded = true;
            }
        </script>
        <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyDhJ23T1FKcfmvdjLKnv6eKq9fCVHdDJyI&language=zh-CN&region=CN&callback=initMap" async defer></script>
    </body>
    </html>
    